<template>
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
    <g fill="none" fill-rule="evenodd">
      <path
        :fill="lightColor"
        d="M3.3,8.4 L0.45,5.55 C-0.15,4.95 -0.15,4.05 0.45,3.45 L3.45,0.45 C4.05,-0.15 4.95,-0.15 5.55,0.45 L8.4,3.3 L3.3,8.4 Z M20.7,15.6 L23.55,18.45 C23.85,18.75 24,19.05 24,19.5 L24,24 L19.5,24 C19.05,24 18.75,23.85 18.45,23.55 L15.6,20.7 L20.7,15.6 Z"
      />
      <path
        :fill="darkColor"
        fill-rule="nonzero"
        d="M23.55,6.45 L17.55,0.45 C16.95,-0.15 16.05,-0.15 15.45,0.45 L13.5,2.4 L16.05,4.95 L13.95,7.05 L11.4,4.5 L9,6.9 L11.55,9.45 L9.45,11.55 L6.9,9 L4.5,11.4 L7.05,13.95 L4.95,16.05 L2.4,13.5 L0.45,15.45 C-0.15,16.05 -0.15,16.95 0.45,17.55 L6.45,23.55 C7.05,24.15 7.95,24.15 8.55,23.55 L23.55,8.55 C24.15,7.95 24.15,7.05 23.55,6.45 Z"
      />
    </g>
  </svg>
</template>
<script>
import Base from '../base.vue';
export default {
  name: 'IconInvoice',
  extends: Base,
};
</script>
