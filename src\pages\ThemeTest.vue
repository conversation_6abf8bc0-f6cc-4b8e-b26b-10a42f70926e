<template>
  <div class="theme-test-page">
    <PageHeader :title="t`اختبار الثيم`" />
    
    <div class="p-6 space-y-6">
      <!-- معلومات الثيم الحالي -->
      <div class="content-card p-6">
        <h2 class="theme-text-primary text-xl font-semibold mb-4">{{ t`الثيم الحالي` }}</h2>
        <p class="theme-text-secondary">
          {{ t`الثيم النشط:` }} <span class="theme-text-accent font-medium">{{ currentTheme }}</span>
        </p>

        <!-- زر تغيير الثيم -->
        <div class="mt-4">
          <ThemeSelector @theme-changed="onThemeChanged" />
        </div>
      </div>

      <!-- اختبار الألوان -->
      <div class="content-card p-6">
        <h2 class="theme-text-primary text-xl font-semibold mb-4">{{ t`اختبار الألوان` }}</h2>
        <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
          <div class="text-center">
            <div class="w-16 h-16 mx-auto mb-2 rounded-lg" style="background: var(--sidebar-bg)"></div>
            <p class="theme-text-secondary text-sm">{{ t`الشريط الجانبي` }}</p>
          </div>
          <div class="text-center">
            <div class="w-16 h-16 mx-auto mb-2 rounded-lg" style="background: var(--content-bg); border: 2px solid var(--text-accent)"></div>
            <p class="theme-text-secondary text-sm">{{ t`المحتوى` }}</p>
          </div>
          <div class="text-center">
            <div class="w-16 h-16 mx-auto mb-2 rounded-lg" style="background: var(--text-primary)"></div>
            <p class="theme-text-secondary text-sm">{{ t`النص الأساسي` }}</p>
          </div>
          <div class="text-center">
            <div class="w-16 h-16 mx-auto mb-2 rounded-lg" style="background: var(--text-accent)"></div>
            <p class="theme-text-secondary text-sm">{{ t`النص المميز` }}</p>
          </div>
          <div class="text-center">
            <div class="w-16 h-16 mx-auto mb-2 rounded-lg" style="background: var(--text-muted)"></div>
            <p class="theme-text-secondary text-sm">{{ t`النص الخافت` }}</p>
          </div>
        </div>

        <!-- عرض الخطوط -->
        <div class="mt-6">
          <h3 class="theme-text-primary text-lg font-medium mb-3">{{ t`أمثلة على الخطوط` }}</h3>
          <div class="space-y-3">
            <p class="theme-text-primary text-lg font-semibold">{{ t`نص أساسي - عنوان رئيسي` }}</p>
            <p class="theme-text-secondary">{{ t`نص ثانوي - محتوى عادي` }}</p>
            <p class="theme-text-accent">{{ t`نص مميز - روابط وعناصر تفاعلية` }}</p>
            <p class="theme-text-muted">{{ t`نص خافت - معلومات إضافية` }}</p>
          </div>
        </div>
      </div>

      <!-- اختبار المكونات -->
      <div class="content-card p-6">
        <h2 class="text-xl font-semibold mb-4">{{ t`اختبار المكونات` }}</h2>
        
        <!-- أزرار -->
        <div class="mb-6">
          <h3 class="text-lg font-medium mb-3">{{ t`الأزرار` }}</h3>
          <div class="space-x-4 space-x-reverse">
            <button class="btn-primary px-4 py-2 rounded-lg text-white">
              {{ t`زر أساسي` }}
            </button>
            <button class="px-4 py-2 rounded-lg border border-gray-300 hover:bg-gray-50">
              {{ t`زر ثانوي` }}
            </button>
          </div>
        </div>

        <!-- نماذج -->
        <div class="mb-6">
          <h3 class="text-lg font-medium mb-3">{{ t`النماذج` }}</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <input 
              type="text" 
              placeholder="حقل نص"
              class="form-input px-3 py-2 w-full"
            />
            <select class="form-input px-3 py-2 w-full">
              <option>{{ t`خيار 1` }}</option>
              <option>{{ t`خيار 2` }}</option>
            </select>
          </div>
        </div>

        <!-- بطاقات -->
        <div class="mb-6">
          <h3 class="text-lg font-medium mb-3">{{ t`البطاقات` }}</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="dashboard-card">
              <h4 class="font-medium mb-2">{{ t`بطاقة 1` }}</h4>
              <p class="text-gray-600 dark:text-gray-400">{{ t`محتوى البطاقة` }}</p>
            </div>
            <div class="dashboard-card">
              <h4 class="font-medium mb-2">{{ t`بطاقة 2` }}</h4>
              <p class="text-gray-600 dark:text-gray-400">{{ t`محتوى البطاقة` }}</p>
            </div>
            <div class="dashboard-card">
              <h4 class="font-medium mb-2">{{ t`بطاقة 3` }}</h4>
              <p class="text-gray-600 dark:text-gray-400">{{ t`محتوى البطاقة` }}</p>
            </div>
          </div>
        </div>

        <!-- جدول -->
        <div>
          <h3 class="text-lg font-medium mb-3">{{ t`الجداول` }}</h3>
          <div class="table-container">
            <table class="w-full">
              <thead class="table-header">
                <tr>
                  <th class="px-4 py-3 text-right">{{ t`العنصر` }}</th>
                  <th class="px-4 py-3 text-right">{{ t`القيمة` }}</th>
                  <th class="px-4 py-3 text-right">{{ t`الحالة` }}</th>
                </tr>
              </thead>
              <tbody>
                <tr class="table-row border-b">
                  <td class="px-4 py-3">{{ t`عنصر 1` }}</td>
                  <td class="px-4 py-3">100</td>
                  <td class="px-4 py-3">
                    <span class="notification success inline-block px-2 py-1 text-xs rounded">
                      {{ t`نشط` }}
                    </span>
                  </td>
                </tr>
                <tr class="table-row border-b">
                  <td class="px-4 py-3">{{ t`عنصر 2` }}</td>
                  <td class="px-4 py-3">200</td>
                  <td class="px-4 py-3">
                    <span class="notification warning inline-block px-2 py-1 text-xs rounded">
                      {{ t`معلق` }}
                    </span>
                  </td>
                </tr>
                <tr class="table-row">
                  <td class="px-4 py-3">{{ t`عنصر 3` }}</td>
                  <td class="px-4 py-3">300</td>
                  <td class="px-4 py-3">
                    <span class="notification error inline-block px-2 py-1 text-xs rounded">
                      {{ t`خطأ` }}
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- معلومات تقنية -->
      <div class="content-card p-6">
        <h2 class="text-xl font-semibold mb-4">{{ t`معلومات تقنية` }}</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <strong>{{ t`متغيرات CSS:` }}</strong>
            <ul class="mt-2 space-y-1 text-gray-600 dark:text-gray-400">
              <li>--sidebar-bg: {{ getCSSVariable('--sidebar-bg') }}</li>
              <li>--content-bg: {{ getCSSVariable('--content-bg') }}</li>
              <li>--primary-500: {{ getCSSVariable('--primary-500') }}</li>
            </ul>
          </div>
          <div>
            <strong>{{ t`الثيمات المتاحة:` }}</strong>
            <ul class="mt-2 space-y-1 text-gray-600 dark:text-gray-400">
              <li v-for="(theme, key) in availableThemes" :key="key">
                {{ theme.name }} ({{ key }})
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { getCurrentTheme, themes } from 'src/utils/theme';
import PageHeader from 'src/components/PageHeader.vue';
import ThemeSelector from 'src/components/ThemeSelector.vue';

export default defineComponent({
  name: 'ThemeTest',
  components: {
    PageHeader,
    ThemeSelector,
  },
  data() {
    return {
      currentTheme: getCurrentTheme(),
      availableThemes: themes,
    };
  },
  methods: {
    onThemeChanged(themeName: string) {
      this.currentTheme = themeName;
    },

    getCSSVariable(variableName: string): string {
      try {
        return getComputedStyle(document.documentElement)
          .getPropertyValue(variableName)
          .trim() || 'غير محدد';
      } catch (error) {
        return 'خطأ';
      }
    },
  },
});
</script>

<style scoped>
.theme-test-page {
  min-height: 100vh;
}
</style>
