import chokidar from 'chokidar';
import esbuild from 'esbuild';
import { $ } from 'execa';
import path from 'path';
import { fileURLToPath } from 'url';
import { getMainProcessCommonConfig } from './helpers.mjs';

process.env['ELECTRON_DISABLE_SECURITY_WARNINGS'] = 'true';
process.env['NODE_ENV'] = 'development';

const host = '0.0.0.0';
// سنحاول استخدام المنافذ التالية بالترتيب
const possiblePorts = [4000, 4001, 4002, 4003, 4004, 4005];
const displayHost = 'localhost';

// سنقوم بتعيين المنفذ في وقت لاحق بعد التحقق من المنافذ المتاحة
let selectedPort = possiblePorts[0];

const dirname = path.dirname(fileURLToPath(import.meta.url));
const root = path.join(dirname, '..', '..');
const $$ = $({ stdio: 'inherit' });
let isReload = false;

let electronProcess = null;

console.log(`running Frappe Books in dev mode\nroot: ${root}`);

// وظيفة للتحقق مما إذا كان المنفذ متاحًا
async function findAvailablePort() {
  const { createServer } = await import('net');

  // استخدام منفذ مختلف تمامًا لتجنب المشاكل
  // سنستخدم منفذ 5173 وهو المنفذ الافتراضي لـ Vite
  const fixedPort = 5173;

  try {
    const server = createServer();

    const portAvailable = await new Promise((resolve) => {
      server.once('error', (err) => {
        console.log(`Port ${fixedPort} is not available:`, err.message);
        resolve(false);
      });

      server.once('listening', () => {
        server.close();
        resolve(true);
      });

      server.listen(fixedPort);
    });

    if (portAvailable) {
      console.log(`Using port: ${fixedPort}`);
      return fixedPort;
    } else {
      // إذا كان المنفذ 5173 غير متاح، نستخدم منفذ آخر
      const alternativePort = 5174;
      console.log(`Trying alternative port: ${alternativePort}`);
      return alternativePort;
    }
  } catch (err) {
    console.log(`Port check error:`, err.message);
    // في حالة حدوث خطأ، نستخدم منفذ آخر
    console.log(`Falling back to port 5174`);
    return 5174;
  }
}

// البحث عن منفذ متاح وتعيينه
selectedPort = await findAvailablePort();
process.env['VITE_HOST'] = host;
process.env['VITE_PORT'] = selectedPort;
process.env['VITE_DISPLAY_HOST'] = displayHost;

console.log(`Starting Vite server on port ${selectedPort}`);
const viteProcess = $$`yarn vite`;

const ctx = await esbuild.context({
  ...getMainProcessCommonConfig(root),
  outdir: path.join(root, 'dist_electron', 'dev'),
});

const fswatcher = chokidar.watch([
  path.join(root, 'main.ts'),
  path.join(root, 'main'),
  path.join(root, 'backend'),
  path.join(root, 'schemas'),
]);

const terminate = async () => {
  await fswatcher.close();
  await ctx.dispose();

  if (electronProcess) {
    electronProcess.kill();
  }

  if (viteProcess) {
    viteProcess.kill();
  }
  process.exit();
};
process.on('SIGINT', terminate);
process.on('SIGTERM', terminate);
if (viteProcess) {
  viteProcess.on('close', terminate);
}

await handleResult(await ctx.rebuild());
electronProcess = runElectron();

fswatcher.on('change', async (path) => {
  console.log(`change detected:\n\t${path}`);
  const result = await ctx.rebuild();
  await handleResult(result);
  console.log(`main process source rebuilt\nrestarting electron`);

  if (electronProcess) {
    isReload = true;
    electronProcess.kill();
    electronProcess = runElectron();
  }
});

async function handleResult(result) {
  if (!result.errors.length) {
    return;
  }

  console.log('error on build');
  for (const error of result.errors) {
    console.log(error);
  }

  await terminate();
}

function runElectron() {
  const electronProcess = $$`npx electron --inspect=5858 ${path.join(
    root,
    'dist_electron',
    'dev',
    'main.js'
  )}`;

  electronProcess.on('close', async () => {
    if (isReload) {
      return;
    }

    await terminate();
  });

  return electronProcess;
}
