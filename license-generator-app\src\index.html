<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد التراخيص - أداة المطور</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .form-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid #e9ecef;
        }

        .form-section h2 {
            color: #495057;
            margin-bottom: 20px;
            font-size: 1.4em;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }

        .form-group input[readonly] {
            background-color: #f8f9fa;
            color: #6c757d;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40,167,69,0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
        }

        .btn-copy {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
            padding: 8px 15px;
            font-size: 12px;
            margin-right: 10px;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .result-section {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            border: 1px solid #c3e6cb;
            border-radius: 10px;
            padding: 25px;
            margin-top: 25px;
        }

        .result-section h3 {
            color: #155724;
            margin-bottom: 15px;
        }

        .license-key {
            background: white;
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            color: #155724;
            margin: 10px 0;
            word-break: break-all;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }

        .info-item {
            background: white;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }

        .info-item strong {
            color: #495057;
        }

        .history-section {
            margin-top: 30px;
        }

        .history-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .history-table th,
        .history-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #e9ecef;
        }

        .history-table th {
            background: linear-gradient(135deg, #495057, #343a40);
            color: white;
            font-weight: 600;
        }

        .history-table tr:hover {
            background-color: #f8f9fa;
        }

        .badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .badge-trial {
            background-color: #fff3cd;
            color: #856404;
        }

        .badge-permanent {
            background-color: #d1ecf1;
            color: #0c5460;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .alert-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .alert-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .flex-row {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .flex-row input {
            flex: 1;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .content {
                padding: 20px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 مولد التراخيص</h1>
            <p>أداة المطور لإنشاء مفاتيح الترخيص</p>
        </div>

        <div class="content">
            <!-- قسم إنشاء الترخيص -->
            <div class="form-section">
                <h2>📝 إنشاء ترخيص جديد</h2>
                
                <div class="form-group">
                    <label for="serialNumber">الرقم التسلسلي للعميل:</label>
                    <div class="flex-row">
                        <input type="text" id="serialNumber" placeholder="XXXX-XXXX-XXXX-XXXX" required>
                        <button type="button" class="btn btn-secondary" onclick="getMySerial()">رقمي التسلسلي</button>
                    </div>
                </div>

                <div class="form-group">
                    <label for="licenseType">نوع الترخيص:</label>
                    <select id="licenseType" onchange="toggleDuration()">
                        <option value="trial">تجريبي</option>
                        <option value="permanent">دائم</option>
                    </select>
                </div>

                <div class="form-group" id="durationGroup">
                    <label for="duration">مدة الترخيص (بالأيام):</label>
                    <input type="number" id="duration" value="7" min="1" max="365">
                </div>

                <div class="form-group">
                    <label for="clientName">اسم العميل (اختياري):</label>
                    <input type="text" id="clientName" placeholder="اسم الشركة أو العميل">
                </div>

                <div class="form-group">
                    <label for="notes">ملاحظات (اختياري):</label>
                    <textarea id="notes" rows="3" placeholder="ملاحظات حول الترخيص"></textarea>
                </div>

                <button type="button" class="btn btn-primary" onclick="generateLicense()">
                    🔑 إنشاء مفتاح الترخيص
                </button>
            </div>

            <!-- قسم التحميل -->
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>جاري إنشاء مفتاح الترخيص...</p>
            </div>

            <!-- قسم النتيجة -->
            <div class="result-section" id="resultSection" style="display: none;">
                <h3>✅ تم إنشاء مفتاح الترخيص بنجاح</h3>
                
                <div class="form-group">
                    <label>مفتاح الترخيص:</label>
                    <div class="flex-row">
                        <div class="license-key" id="licenseKey"></div>
                        <button type="button" class="btn btn-copy" onclick="copyLicenseKey()">📋 نسخ</button>
                    </div>
                </div>

                <div class="info-grid" id="licenseInfo"></div>
            </div>

            <!-- قسم السجل -->
            <div class="history-section">
                <div class="form-section">
                    <h2>📊 سجل التراخيص المُنشأة</h2>
                    <div style="margin-bottom: 15px;">
                        <button type="button" class="btn btn-secondary" onclick="refreshHistory()">🔄 تحديث</button>
                        <button type="button" class="btn btn-success" onclick="exportHistory()">📤 تصدير</button>
                    </div>
                    <div style="overflow-x: auto;">
                        <table class="history-table">
                            <thead>
                                <tr>
                                    <th>النوع</th>
                                    <th>العميل</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="historyTableBody">
                                <!-- سيتم ملء البيانات هنا -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="renderer.js"></script>
</body>
</html>
