<template>
  <div
    class="
      flex
      items-center
      justify-center
      h-full
      bg-gray-50
      dark:bg-gray-900
    "
  >
    <div
      class="
        w-96
        p-8
        bg-white
        dark:bg-gray-800
        rounded-lg
        shadow-lg
        border
        dark:border-gray-700
      "
    >
      <div class="text-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800 dark:text-white">
          {{ t`إصلاح دور المستخدم` }}
        </h1>
        <p class="text-gray-600 dark:text-gray-400 mt-2">
          {{ t`هذه الصفحة تقوم بإصلاح دور المستخدم المحدد` }}
        </p>
      </div>

      <div v-if="errorMessage" class="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
        {{ errorMessage }}
      </div>

      <div v-if="successMessage" class="mb-4 p-3 bg-green-100 text-green-700 rounded-md">
        {{ successMessage }}
      </div>

      <form @submit.prevent="fixUserRole">
        <div class="mb-4">
          <label
            for="username"
            class="block text-gray-700 dark:text-gray-300 mb-2"
          >
            {{ t`اسم المستخدم` }}
          </label>
          <input
            id="username"
            v-model="username"
            type="text"
            class="
              w-full
              px-3
              py-2
              border
              rounded-md
              focus:outline-none
              focus:ring-2
              focus:ring-blue-500
              dark:bg-gray-700
              dark:border-gray-600
              dark:text-white
            "
            required
          />
        </div>

        <div class="mb-4">
          <label
            for="role"
            class="block text-gray-700 dark:text-gray-300 mb-2"
          >
            {{ t`الدور` }}
          </label>
          <select
            id="role"
            v-model="role"
            class="
              w-full
              px-3
              py-2
              border
              rounded-md
              focus:outline-none
              focus:ring-2
              focus:ring-blue-500
              dark:bg-gray-700
              dark:border-gray-600
              dark:text-white
            "
            required
          >
            <option value="Admin">{{ t`مدير` }}</option>
            <option value="User">{{ t`مستخدم` }}</option>
          </select>
        </div>

        <button
          type="submit"
          class="
            w-full
            py-2
            px-4
            bg-blue-600
            hover:bg-blue-700
            text-white
            rounded-md
            focus:outline-none
            focus:ring-2
            focus:ring-blue-500
            focus:ring-offset-2
          "
          :disabled="isLoading"
        >
          <span v-if="isLoading">{{ t`جاري إصلاح الدور...` }}</span>
          <span v-else>{{ t`إصلاح الدور` }}</span>
        </button>
      </form>

      <div class="mt-4 text-center">
        <a
          href="/login"
          class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
        >
          {{ t`العودة إلى صفحة تسجيل الدخول` }}
        </a>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { fyo } from 'src/initFyo';
import { showToast } from 'src/utils/interactive';
import { User } from 'models/baseModels/User/User';

export default defineComponent({
  name: 'FixUserRole',
  data() {
    return {
      username: '',
      role: 'Admin',
      errorMessage: '',
      successMessage: '',
      isLoading: false,
    };
  },
  methods: {
    async fixUserRole() {
      this.isLoading = true;
      this.errorMessage = '';
      this.successMessage = '';

      try {
        // التحقق من وجود المستخدم
        const exists = await fyo.db.exists('User', this.username);
        if (!exists) {
          this.errorMessage = this.t`اسم المستخدم غير موجود`;
          this.isLoading = false;
          return;
        }

        // الحصول على وثيقة المستخدم
        const user = await fyo.doc.getDoc('User', this.username);
        
        // طباعة معلومات المستخدم الحالية
        console.log('Current user data:', {
          name: user.name,
          role: user.role,
          roleType: typeof user.role
        });
        
        // تعيين دور المستخدم
        console.log('Setting role to:', this.role);
        await user.set('role', this.role);
        await user.sync();
        
        // طباعة معلومات المستخدم بعد التحديث
        console.log('Updated user data:', {
          name: user.name,
          role: user.role,
          roleType: typeof user.role
        });
        
        this.successMessage = this.t`تم إصلاح دور المستخدم بنجاح`;
        
        // مسح النموذج
        this.username = '';
        
        showToast({
          message: this.t`تم إصلاح دور المستخدم بنجاح`,
          type: 'success',
        });
      } catch (error) {
        console.error('Error fixing user role:', error);
        this.errorMessage = this.t`حدث خطأ أثناء إصلاح دور المستخدم: ${error.message || error}`;
      } finally {
        this.isLoading = false;
      }
    },
  },
});
</script>
