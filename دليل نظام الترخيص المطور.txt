# دليل نظام الترخيص المطور

## نظرة عامة

تم تطوير نظام ترخيص متقدم يعتمد على الرقم التسلسلي للجهاز لضمان الأمان والتحكم الكامل في التراخيص.

## كيفية عمل النظام

### 1. عند تثبيت التطبيق عند العميل

1. **إنشاء الرقم التسلسلي**:
   - يتم إنشاء رقم تسلسلي فريد للجهاز بناءً على:
     - نظام التشغيل (platform)
     - معمارية المعالج (architecture)
     - نموذج المعالج (CPU model)
     - إجمالي الذاكرة (total memory)
     - اسم الجهاز (hostname)
     - اسم المستخدم (username)
     - عنوان MAC الأساسي (primary MAC address)

2. **تنسيق الرقم التسلسلي**:
   ```
   XXXX-XXXX-XXXX-XXXX
   مثال: A1B2-C3D4-E5F6-G7H8
   ```

3. **عرض الرقم للعميل**:
   - يظهر الرقم التسلسلي في صفحة تفعيل الترخيص
   - يمكن للعميل نسخ الرقم وإرساله للمطور

### 2. إنشاء مفتاح الترخيص (للمطور)

#### الطريقة الأولى: استخدام صفحة مولد التراخيص
1. انتقل إلى `/license-generator` في النظام
2. أدخل الرقم التسلسلي المُرسل من العميل
3. اختر نوع الترخيص:
   - **تجريبي**: مدة محددة (افتراضي 7 أيام)
   - **دائم**: ترخيص دائم (10 سنوات)
4. أدخل اسم العميل والملاحظات (اختياري)
5. انقر على "إنشاء مفتاح الترخيص"
6. انسخ المفتاح وأرسله للعميل

#### الطريقة الثانية: استخدام الكود البرمجي
```javascript
import { LicenseManager } from 'src/utils/licenseManager';

// إنشاء مفتاح تجريبي (7 أيام)
const trialKey = LicenseManager.createLicenseKey(
  'A1B2-C3D4-E5F6-G7H8', // الرقم التسلسلي
  'trial',               // نوع الترخيص
  7                      // المدة بالأيام
);

// إنشاء مفتاح دائم
const permanentKey = LicenseManager.createLicenseKey(
  'A1B2-C3D4-E5F6-G7H8', // الرقم التسلسلي
  'permanent'            // نوع الترخيص
);
```

### 3. تنسيق مفتاح الترخيص

#### مفتاح تجريبي:
```
TRL-XXXX-XXXX-XXXX-XXXX
مثال: TRL-A1B2-C3D4-E5F6-G7H8
```

#### مفتاح دائم:
```
PRM-XXXX-XXXX-XXXX-XXXX
مثال: PRM-A1B2-C3D4-E5F6-G7H8
```

### 4. تفعيل الترخيص عند العميل

1. العميل يدخل مفتاح الترخيص في صفحة التفعيل
2. النظام يتحقق من:
   - صحة تنسيق المفتاح
   - تطابق المفتاح مع الرقم التسلسلي للجهاز
   - نوع الترخيص (تجريبي أو دائم)
3. إذا كان المفتاح صحيح، يتم تفعيل الترخيص
4. يتم حفظ بيانات الترخيص في قاعدة البيانات

## أنواع التراخيص

### 1. الترخيص التجريبي (Trial)
- **المدة**: قابلة للتخصيص (افتراضي 7 أيام)
- **عدد المستخدمين**: 3 مستخدمين
- **الوحدات المسموحة**: المبيعات، المخزون، العام
- **الاستخدام**: للتجربة والاختبار

### 2. الترخيص الدائم (Permanent)
- **المدة**: 10 سنوات (عملياً دائم)
- **عدد المستخدمين**: 50 مستخدم
- **الوحدات المسموحة**: جميع الوحدات
- **الاستخدام**: للاستخدام التجاري الكامل

## الأمان والحماية

### 1. ربط الترخيص بالجهاز
- كل ترخيص مرتبط بجهاز محدد عبر الرقم التسلسلي
- لا يمكن استخدام الترخيص على جهاز آخر
- يتم التحقق من معرف الجهاز عند كل تشغيل

### 2. التشفير والحماية
- يتم إنشاء المفاتيح باستخدام SHA-256
- الرقم التسلسلي مشفر ومدمج في المفتاح
- لا يمكن تزوير أو تعديل المفاتيح

### 3. التحقق الدوري
- يتم التحقق من الترخيص كل 24 ساعة
- يتم تسجيل عدد مرات التحقق
- تحذيرات قبل انتهاء الصلاحية

## إدارة التراخيص

### 1. معلومات الترخيص
- عرض حالة الترخيص الحالي
- تفاصيل الترخيص (النوع، تاريخ الانتهاء، إلخ)
- الوحدات المسموحة
- إحصائيات الاستخدام

### 2. سجل التراخيص (للمطور)
- سجل بجميع التراخيص المُنشأة
- معلومات العملاء والتواريخ
- إمكانية نسخ المفاتيح السابقة

### 3. إلغاء التفعيل
- يمكن إلغاء تفعيل الترخيص
- يتم منع الوصول للنظام فوراً
- يمكن إعادة التفعيل بمفتاح جديد

## الملفات المُعدلة

### ملفات النظام الأساسية
1. `models/baseModels/License/License.ts` - نموذج الترخيص المطور
2. `src/utils/licenseManager.ts` - خدمة إدارة الترخيص المطورة
3. `src/pages/LicenseActivation.vue` - صفحة التفعيل مع الرقم التسلسلي
4. `src/pages/LicenseGenerator.vue` - أداة مولد التراخيص للمطور (جديد)
5. `src/router.ts` - المسارات المحدثة
6. `src/utils/sidebarConfig.ts` - القائمة الجانبية المحدثة

### الدوال الجديدة المضافة

#### في `License.ts`:
- `generateSerialNumber()` - إنشاء الرقم التسلسلي
- `validateSerialNumber()` - التحقق من الرقم التسلسلي
- `generateLicenseKey()` - إنشاء مفتاح الترخيص
- `validateLicenseKeyWithSerial()` - التحقق من المفتاح مع الرقم التسلسلي

#### في `licenseManager.ts`:
- `getSerialNumber()` - الحصول على الرقم التسلسلي
- `createLicenseKey()` - إنشاء مفتاح (للمطور)
- `validateSerialNumber()` - التحقق من الرقم التسلسلي

## خطوات التثبيت والتفعيل

### للعميل:
1. تثبيت التطبيق
2. تشغيل التطبيق لأول مرة
3. نسخ الرقم التسلسلي المعروض
4. إرسال الرقم للمطور
5. استلام مفتاح الترخيص
6. إدخال المفتاح في صفحة التفعيل
7. بدء استخدام النظام

### للمطور:
1. استلام الرقم التسلسلي من العميل
2. فتح صفحة مولد التراخيص
3. إدخال الرقم التسلسلي
4. اختيار نوع الترخيص والمدة
5. إنشاء المفتاح
6. إرسال المفتاح للعميل

## استكشاف الأخطاء

### مشاكل شائعة وحلولها:

1. **"الرقم التسلسلي غير صحيح"**:
   - تأكد من نسخ الرقم كاملاً
   - تأكد من عدم وجود مسافات إضافية

2. **"مفتاح الترخيص غير صحيح"**:
   - تأكد من إدخال المفتاح كاملاً
   - تأكد من أن المفتاح مُنشأ للرقم التسلسلي الصحيح

3. **"الترخيص منتهي الصلاحية"**:
   - تحقق من تاريخ انتهاء الترخيص
   - اطلب ترخيص جديد من المطور

4. **"تم الوصول للحد الأقصى للمستخدمين"**:
   - تحقق من عدد المستخدمين النشطين
   - احذف المستخدمين غير المستخدمين
   - اطلب ترخيص بحد أعلى للمستخدمين

## الصيانة والتطوير

### للمطور:
1. احتفظ بسجل لجميع التراخيص المُصدرة
2. راقب انتهاء صلاحية التراخيص
3. قم بتحديث النظام دورياً
4. احتفظ بنسخ احتياطية من بيانات التراخيص

### للعميل:
1. احتفظ بنسخة من مفتاح الترخيص
2. راقب تحذيرات انتهاء الصلاحية
3. تواصل مع المطور قبل انتهاء الترخيص
4. لا تشارك مفتاح الترخيص مع آخرين

## معلومات التواصل

للحصول على تراخيص جديدة أو الدعم التقني:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966 XX XXX XXXX
- الموقع الإلكتروني: www.company.com
