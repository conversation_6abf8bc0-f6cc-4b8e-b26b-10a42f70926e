<main class="bg-white h-full" :style="{ 'font-family': print.font }">
  <!-- Invoice Header -->
  <header class="bg-gray-100 px-12 py-10">
    <!-- Company Details -->
    <section class="flex items-center">
      <img
        v-if="print.displayLogo"
        class="h-12 max-w-32 object-contain mr-4"
        :src="print.logo"
      />

      <div>
        <p class="font-semibold text-xl" :style="{ color: print.color }">
          {{ print.companyName }}
        </p>

        <p class="text-sm text-gray-800" v-if="print.address">
          {{ print.links.address.addressDisplay }}
        </p>

        <p class="text-sm text-gray-800" v-if="print.gstin">
          GSTIN: {{ print.gstin }}
        </p>
      </div>
    </section>

    <!-- Sub Heading Section -->
    <div class="mt-8 text-lg">
      <!-- Doc <PERSON> -->
      <section class="flex">
        <h3 class="w-1/3 font-semibold">{{t`Bill`}}</h3>
        <div class="w-2/3 text-gray-800">
          <p class="font-semibold">{{ doc.name }}</p>

          <div class="flex gap-2">
            <p>{{ doc.date }}</p>
            <p>{{doc?.time }}</p>
          </div>
        </div>
      </section>

      <!-- Party Details -->
      <section class="mt-4 flex">
        <h3 class="w-1/3 font-semibold">{{t`party`}}</h3>

        <div class="w-2/3 text-gray-800" v-if="doc.party">
          <p class="font-semibold">{{ doc.party }}</p>

          <p v-if="doc.links.party.address">
            {{ doc.links.party.links.address.addressDisplay }}
          </p>

          <p v-if="doc.links.party.gstin">GSTIN: {{ doc.links.party.gstin }}</p>
        </div>
      </section>
    </div>
  </header>

  <!-- Payment Details -->
  <section class="px-12 py-12 text-lg">
    <div class="w-3/4 grid grid-cols-7 grid-rows-2 p-2 gap-3">
      <div class="col-span-2 font-bold"><p>{{t`Payment Type`}}</p></div>
      <div class="col-start-3">:</div>
      <div class="col-span-4 col-start-4">{{ doc.paymentType }}</div>

      <div class="col-span-2 row-start-2 font-bold">{{t`Payment Method`}}</div>
      <div class="col-start-3 row-start-2">:</div>
      <div class="col-span-4 col-start-4 row-start-2">
        {{ doc.paymentMethod }}
      </div>
    </div>
  </section>

  <section class="w-1/2 px-12 pb-6 text-lg" v-if="doc.taxes.length">
    <p class="flex justify-center font-semibold text-lg mt-2">
      {{t`Tax Summary`}}
    </p>

    <div
      class="flex justify-between pl-5 text-base"
      v-for="tax in doc?.taxes"
      :key="tax.name"
    >
      <p>{{ tax.account }}</p>
      <p>{{ tax.amount }}</p>
    </div>

    <div class="flex justify-between pl-5 text-base">
      <p>{{t`Total Ex.Tax`}}</p>
      <p>{{doc.subTotal}}</p>
    </div>
  </section>
  <!-- Invoice Footer -->
  <footer class="px-12 text-lg">
    <section class="pt-5 text-lg">
      <div class="w-full grid grid-cols-7 grid-rows-5 p-2 gap-3">
        <div class="col-span-2 font-bold">
          <p>{{t`Grand Total`}}</p>
        </div>

        <div class="col-start-3">:</div>

        <div class="col-span-4 col-start-4 font-bold">
          <p>{{ doc.amount }}</p>
          <p class="font-normal">{{ doc.grandTotalInWords }}</p>
        </div>

        <div class="col-span-2 row-start-2 font-bold">{{t`Amount Paid`}}</div>

        <div class="col-start-3 row-start-2">:</div>

        <div class="col-span-4 col-start-4 row-start-2 font-bold">
          <p>{{ doc.amountPaid }}</p>
          <p class="font-normal">{{ doc.amountPaidInWords }}</p>
        </div>
      </div>
    </section>

    <!-- Invoice Terms -->
    <section class="mt-12" v-if="doc.terms">
      <h3 class="text-lg font-semibold">{{t`Notes`}}</h3>

      <p class="mt-4 text-lg whitespace-pre-line">{{ doc.terms }}</p>
    </section>
  </footer>
</main>
