/**
 * وظائف مساعدة للتشفير تعمل في المتصفح
 */

/**
 * تشفير نص باستخدام خوارزمية بسيطة
 * ملاحظة: هذه ليست طريقة آمنة للإنتاج، ولكنها تعمل للاختبار
 * @param text النص المراد تشفيره
 * @returns النص المشفر
 */
export function simpleHash(text: string): string {
  // استخدام طريقة أبسط وأكثر ثباتًا للتشفير
  // هذه ليست طريقة آمنة للإنتاج، ولكنها تعمل للاختبار

  // للتبسيط، سنستخدم طريقة ثابتة جدًا
  // في بيئة الإنتاج، يجب استخدام خوارزمية تشفير قوية مثل bcrypt أو Argon2

  // تحويل النص إلى سلسلة من الأرقام السداسية عشرية
  let result = '';
  for (let i = 0; i < text.length; i++) {
    const charCode = text.charCodeAt(i);
    // تنسيق كل رمز ليكون دائمًا رقمين سداسي عشري
    result += charCode.toString(16).padStart(2, '0');
  }

  console.log(`Hashing "${text}" to "${result}"`);
  return result;
}

/**
 * تشفير نص باستخدام خوارزمية أكثر تعقيدًا
 * @param text النص المراد تشفيره
 * @returns وعد بالنص المشفر
 */
export async function secureHash(text: string): Promise<string> {
  // استخدام SubtleCrypto API المتوفر في المتصفحات الحديثة
  if (window.crypto && window.crypto.subtle) {
    try {
      const encoder = new TextEncoder();
      const data = encoder.encode(text);
      const hashBuffer = await window.crypto.subtle.digest('SHA-256', data);
      const hashArray = Array.from(new Uint8Array(hashBuffer));
      const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
      return hashHex;
    } catch (error) {
      console.error('Error using SubtleCrypto:', error);
      // استخدام الطريقة البسيطة كبديل
      return simpleHash(text);
    }
  }

  // استخدام الطريقة البسيطة إذا كان SubtleCrypto غير متوفر
  return simpleHash(text);
}

/**
 * التحقق من تطابق النص مع النص المشفر
 * @param text النص الأصلي
 * @param hash النص المشفر
 * @returns وعد بنتيجة التحقق (صحيح/خطأ)
 */
export async function verifyHash(text: string, hash: string): Promise<boolean> {
  const newHash = await secureHash(text);
  return newHash === hash;
}
