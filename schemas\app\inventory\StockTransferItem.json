{"name": "StockTransferItem", "label": "Stock Transfer Item", "isAbstract": true, "isChild": true, "fields": [{"fieldname": "item", "label": "<PERSON><PERSON>", "fieldtype": "Link", "target": "<PERSON><PERSON>", "required": true}, {"fieldname": "location", "fieldtype": "Link", "target": "Location", "required": true}, {"fieldname": "transferUnit", "label": "Transfer Unit", "fieldtype": "Link", "target": "UOM", "default": "Unit", "placeholder": "Unit"}, {"fieldname": "transferQuantity", "label": "Qty. in Transfer Unit", "fieldtype": "Float", "default": 1, "required": true}, {"fieldname": "unit", "label": "Stock Unit", "fieldtype": "Link", "target": "UOM", "default": "Unit", "placeholder": "Unit", "readOnly": true}, {"fieldname": "batch", "label": "<PERSON><PERSON>", "fieldtype": "Link", "target": "<PERSON><PERSON>"}, {"fieldname": "serialNumber", "label": "Serial Number", "fieldtype": "Text"}, {"fieldname": "quantity", "label": "Quantity", "fieldtype": "Float", "required": true, "default": 1}, {"fieldname": "unitConversionFactor", "label": "Conversion Factor", "fieldtype": "Float", "required": true, "default": 1}, {"fieldname": "rate", "label": "Rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "required": true}, {"fieldname": "amount", "label": "Amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "readOnly": true}, {"fieldname": "itemDiscountAmount", "label": "Discount Amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "readOnly": false}, {"fieldname": "itemDiscountPercent", "label": "Discount Percent", "fieldtype": "Float", "readOnly": false}, {"fieldname": "description", "label": "Description", "placeholder": "Item Description", "fieldtype": "Text"}, {"fieldname": "hsnCode", "label": "HSN/SAC", "fieldtype": "Int", "placeholder": "HSN/SAC Code"}], "tableFields": ["item", "location", "quantity", "rate", "amount"], "quickEditFields": ["item", "transferQuantity", "transferUnit", "batch", "serialNumber", "quantity", "unit", "unitConversionFactor", "description", "hsnCode", "location", "rate", "amount", "itemDiscountAmount", "itemDiscountPercent"]}