<template>
  <div class="flex flex-col h-full">
    <PageHeader :title="t`مولد مفاتيح الترخيص`" />

    <div class="flex-1 p-4 overflow-auto">
      <div class="max-w-2xl mx-auto space-y-6">
        <!-- تحذير للمطور -->
        <div class="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="mr-3">
              <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                {{ t`تحذير للمطور` }}
              </h3>
              <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                <p>{{ t`هذه الأداة مخصصة للمطور فقط لإنشاء مفاتيح الترخيص. لا تشارك هذه الصفحة مع العملاء.` }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- نموذج إنشاء مفتاح الترخيص -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">
            {{ t`إنشاء مفتاح ترخيص جديد` }}
          </h2>

          <form @submit.prevent="generateLicense">
            <div class="space-y-4">
              <!-- الرقم التسلسلي -->
              <div>
                <label for="serialNumber" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {{ t`الرقم التسلسلي للعميل` }}
                </label>
                <input
                  id="serialNumber"
                  v-model="licenseForm.serialNumber"
                  type="text"
                  class="
                    w-full
                    px-3
                    py-2
                    border
                    rounded-md
                    focus:outline-none
                    focus:ring-2
                    focus:ring-blue-500
                    dark:bg-gray-700
                    dark:border-gray-600
                    dark:text-white
                    font-mono
                  "
                  placeholder="XXXX-XXXX-XXXX-XXXX"
                  required
                />
              </div>

              <!-- نوع الترخيص -->
              <div>
                <label for="licenseType" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {{ t`نوع الترخيص` }}
                </label>
                <select
                  id="licenseType"
                  v-model="licenseForm.licenseType"
                  class="
                    w-full
                    px-3
                    py-2
                    border
                    rounded-md
                    focus:outline-none
                    focus:ring-2
                    focus:ring-blue-500
                    dark:bg-gray-700
                    dark:border-gray-600
                    dark:text-white
                  "
                  required
                >
                  <option value="trial">{{ t`تجريبي` }}</option>
                  <option value="permanent">{{ t`دائم` }}</option>
                </select>
              </div>

              <!-- مدة الترخيص التجريبي -->
              <div v-if="licenseForm.licenseType === 'trial'">
                <label for="duration" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {{ t`مدة الترخيص (بالأيام)` }}
                </label>
                <input
                  id="duration"
                  v-model.number="licenseForm.duration"
                  type="number"
                  min="1"
                  max="365"
                  class="
                    w-full
                    px-3
                    py-2
                    border
                    rounded-md
                    focus:outline-none
                    focus:ring-2
                    focus:ring-blue-500
                    dark:bg-gray-700
                    dark:border-gray-600
                    dark:text-white
                  "
                />
              </div>

              <!-- اسم العميل -->
              <div>
                <label for="clientName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {{ t`اسم العميل (اختياري)` }}
                </label>
                <input
                  id="clientName"
                  v-model="licenseForm.clientName"
                  type="text"
                  class="
                    w-full
                    px-3
                    py-2
                    border
                    rounded-md
                    focus:outline-none
                    focus:ring-2
                    focus:ring-blue-500
                    dark:bg-gray-700
                    dark:border-gray-600
                    dark:text-white
                  "
                  placeholder="اسم الشركة أو العميل"
                />
              </div>

              <!-- ملاحظات -->
              <div>
                <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {{ t`ملاحظات (اختياري)` }}
                </label>
                <textarea
                  id="notes"
                  v-model="licenseForm.notes"
                  rows="3"
                  class="
                    w-full
                    px-3
                    py-2
                    border
                    rounded-md
                    focus:outline-none
                    focus:ring-2
                    focus:ring-blue-500
                    dark:bg-gray-700
                    dark:border-gray-600
                    dark:text-white
                  "
                  placeholder="ملاحظات حول الترخيص"
                ></textarea>
              </div>

              <!-- زر الإنشاء -->
              <button
                type="submit"
                class="
                  w-full
                  py-2
                  px-4
                  bg-blue-600
                  hover:bg-blue-700
                  text-white
                  rounded-md
                  focus:outline-none
                  focus:ring-2
                  focus:ring-blue-500
                  focus:ring-offset-2
                "
                :disabled="isGenerating"
              >
                <span v-if="isGenerating">{{ t`جاري الإنشاء...` }}</span>
                <span v-else>{{ t`إنشاء مفتاح الترخيص` }}</span>
              </button>
            </div>
          </form>
        </div>

        <!-- عرض مفتاح الترخيص المُنشأ -->
        <div v-if="generatedLicense" class="bg-green-50 dark:bg-green-900 rounded-lg shadow p-6">
          <h2 class="text-xl font-semibold text-green-800 dark:text-green-200 mb-4">
            {{ t`مفتاح الترخيص المُنشأ` }}
          </h2>

          <div class="space-y-4">
            <!-- معلومات الترخيص -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span class="font-medium text-green-700 dark:text-green-300">{{ t`النوع:` }}</span>
                <span class="text-green-600 dark:text-green-400 mr-2">
                  {{ generatedLicense.type === 'trial' ? t`تجريبي` : t`دائم` }}
                </span>
              </div>
              <div v-if="generatedLicense.type === 'trial'">
                <span class="font-medium text-green-700 dark:text-green-300">{{ t`المدة:` }}</span>
                <span class="text-green-600 dark:text-green-400 mr-2">
                  {{ generatedLicense.duration }} {{ t`يوم` }}
                </span>
              </div>
              <div>
                <span class="font-medium text-green-700 dark:text-green-300">{{ t`تاريخ الإنشاء:` }}</span>
                <span class="text-green-600 dark:text-green-400 mr-2">
                  {{ formatDate(generatedLicense.createdAt) }}
                </span>
              </div>
              <div v-if="licenseForm.clientName">
                <span class="font-medium text-green-700 dark:text-green-300">{{ t`العميل:` }}</span>
                <span class="text-green-600 dark:text-green-400 mr-2">
                  {{ licenseForm.clientName }}
                </span>
              </div>
            </div>

            <!-- مفتاح الترخيص -->
            <div>
              <label class="block text-sm font-medium text-green-700 dark:text-green-300 mb-1">
                {{ t`مفتاح الترخيص` }}
              </label>
              <div class="flex items-center space-x-2 space-x-reverse">
                <input
                  :value="generatedLicense.key"
                  readonly
                  class="
                    flex-1
                    px-3
                    py-2
                    border
                    rounded-md
                    bg-green-100
                    dark:bg-green-800
                    dark:border-green-600
                    text-green-800
                    dark:text-green-200
                    font-mono
                    text-center
                  "
                />
                <button
                  @click="copyLicenseKey"
                  class="
                    px-3
                    py-2
                    bg-green-600
                    hover:bg-green-700
                    text-white
                    rounded-md
                  "
                >
                  {{ t`نسخ` }}
                </button>
              </div>
            </div>

            <!-- الرقم التسلسلي -->
            <div>
              <label class="block text-sm font-medium text-green-700 dark:text-green-300 mb-1">
                {{ t`الرقم التسلسلي` }}
              </label>
              <input
                :value="generatedLicense.serialNumber"
                readonly
                class="
                  w-full
                  px-3
                  py-2
                  border
                  rounded-md
                  bg-green-100
                  dark:bg-green-800
                  dark:border-green-600
                  text-green-800
                  dark:text-green-200
                  font-mono
                  text-center
                "
              />
            </div>

            <!-- ملاحظات -->
            <div v-if="licenseForm.notes">
              <label class="block text-sm font-medium text-green-700 dark:text-green-300 mb-1">
                {{ t`الملاحظات` }}
              </label>
              <p class="text-green-600 dark:text-green-400 text-sm">
                {{ licenseForm.notes }}
              </p>
            </div>
          </div>
        </div>

        <!-- سجل التراخيص المُنشأة -->
        <div v-if="licenseHistory.length > 0" class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">
            {{ t`سجل التراخيص المُنشأة` }}
          </h2>

          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    {{ t`النوع` }}
                  </th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    {{ t`العميل` }}
                  </th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    {{ t`تاريخ الإنشاء` }}
                  </th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    {{ t`الإجراءات` }}
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                <tr v-for="(license, index) in licenseHistory" :key="index">
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    <span
                      :class="[
                        'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                        license.type === 'trial' 
                          ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                          : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      ]"
                    >
                      {{ license.type === 'trial' ? t`تجريبي` : t`دائم` }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {{ license.clientName || t`غير محدد` }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {{ formatDate(license.createdAt) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      @click="copyHistoryLicenseKey(license.key)"
                      class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                    >
                      {{ t`نسخ المفتاح` }}
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { LicenseManager } from 'src/utils/licenseManager';
import { showToast } from 'src/utils/interactive';
import PageHeader from 'src/components/PageHeader.vue';

export default defineComponent({
  name: 'LicenseGenerator',
  components: {
    PageHeader,
  },
  data() {
    return {
      licenseForm: {
        serialNumber: '',
        licenseType: 'trial',
        duration: 7,
        clientName: '',
        notes: '',
      },
      isGenerating: false,
      generatedLicense: null as any,
      licenseHistory: [] as any[],
    };
  },
  mounted() {
    // تحميل سجل التراخيص من localStorage
    this.loadLicenseHistory();
  },
  methods: {
    async generateLicense() {
      this.isGenerating = true;
      
      try {
        // التحقق من صحة الرقم التسلسلي
        if (!this.licenseForm.serialNumber || this.licenseForm.serialNumber.length < 10) {
          showToast({
            message: this.t`يرجى إدخال رقم تسلسلي صحيح`,
            type: 'error',
          });
          return;
        }

        // إنشاء مفتاح الترخيص
        const licenseKey = LicenseManager.createLicenseKey(
          this.licenseForm.serialNumber,
          this.licenseForm.licenseType as any,
          this.licenseForm.duration
        );

        // إنشاء كائن الترخيص المُنشأ
        this.generatedLicense = {
          key: licenseKey,
          serialNumber: this.licenseForm.serialNumber,
          type: this.licenseForm.licenseType,
          duration: this.licenseForm.duration,
          clientName: this.licenseForm.clientName,
          notes: this.licenseForm.notes,
          createdAt: new Date(),
        };

        // إضافة إلى السجل
        this.licenseHistory.unshift({ ...this.generatedLicense });
        this.saveLicenseHistory();

        showToast({
          message: this.t`تم إنشاء مفتاح الترخيص بنجاح`,
          type: 'success',
        });

      } catch (error) {
        console.error('Error generating license:', error);
        showToast({
          message: this.t`حدث خطأ أثناء إنشاء مفتاح الترخيص`,
          type: 'error',
        });
      } finally {
        this.isGenerating = false;
      }
    },

    async copyLicenseKey() {
      try {
        await navigator.clipboard.writeText(this.generatedLicense.key);
        showToast({
          message: this.t`تم نسخ مفتاح الترخيص`,
          type: 'success',
        });
      } catch (error) {
        console.error('Error copying license key:', error);
        showToast({
          message: this.t`فشل في نسخ مفتاح الترخيص`,
          type: 'error',
        });
      }
    },

    async copyHistoryLicenseKey(key: string) {
      try {
        await navigator.clipboard.writeText(key);
        showToast({
          message: this.t`تم نسخ مفتاح الترخيص`,
          type: 'success',
        });
      } catch (error) {
        console.error('Error copying license key:', error);
        showToast({
          message: this.t`فشل في نسخ مفتاح الترخيص`,
          type: 'error',
        });
      }
    },

    formatDate(date: Date): string {
      return new Date(date).toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    },

    loadLicenseHistory() {
      try {
        const history = localStorage.getItem('licenseGeneratorHistory');
        if (history) {
          this.licenseHistory = JSON.parse(history);
        }
      } catch (error) {
        console.error('Error loading license history:', error);
      }
    },

    saveLicenseHistory() {
      try {
        // الاحتفاظ بآخر 50 ترخيص فقط
        const historyToSave = this.licenseHistory.slice(0, 50);
        localStorage.setItem('licenseGeneratorHistory', JSON.stringify(historyToSave));
      } catch (error) {
        console.error('Error saving license history:', error);
      }
    },
  },
});
</script>
