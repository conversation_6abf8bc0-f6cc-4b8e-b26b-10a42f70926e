import ChartOfAccounts from 'src/pages/ChartOfAccounts.vue';
import CommonForm from 'src/pages/CommonForm/CommonForm.vue';
import Dashboard from 'src/pages/Dashboard/Dashboard.vue';
import GetStarted from 'src/pages/GetStarted.vue';
import ImportWizard from 'src/pages/ImportWizard.vue';
import ListView from 'src/pages/ListView/ListView.vue';
import PrintView from 'src/pages/PrintView/PrintView.vue';
import ReportPrintView from 'src/pages/PrintView/ReportPrintView.vue';
import QuickEditForm from 'src/pages/QuickEditForm.vue';
import Report from 'src/pages/Report.vue';
import Settings from 'src/pages/Settings/Settings.vue';
import TemplateBuilder from 'src/pages/TemplateBuilder/TemplateBuilder.vue';
import CustomizeForm from 'src/pages/CustomizeForm/CustomizeForm.vue';
import POS from 'src/pages/POS/POS.vue';
import Login from 'src/pages/Login.vue';
import UserManagement from 'src/pages/UserManagement.vue';
import TestPage from 'src/pages/TestPage.vue';
import ResetPassword from 'src/pages/ResetPassword.vue';
import ResetAdminPassword from 'src/pages/ResetAdminPassword.vue';
import FixUserPassword from 'src/pages/FixUserPassword.vue';
import FixUserActive from 'src/pages/FixUserActive.vue';
import FixUserRole from 'src/pages/FixUserRole.vue';
import LicenseActivation from 'src/pages/LicenseActivation.vue';
import LicenseInfo from 'src/pages/LicenseInfo.vue';
import LicenseGenerator from 'src/pages/LicenseGenerator.vue';
import type { HistoryState } from 'vue-router';
import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router';
import { historyState } from './utils/refs';
import { fyo } from './initFyo';
import { LicenseManager } from './utils/licenseManager';

const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    components: {
      login: Login
    },
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    component: Dashboard,
    meta: { requiresAuth: true }
  },
  {
    path: '/get-started',
    component: GetStarted,
  },
  {
    path: `/edit/:schemaName/:name`,
    name: `CommonForm`,
    components: {
      default: CommonForm,
      edit: QuickEditForm,
    },
    props: {
      default: (route) => ({
        schemaName: route.params.schemaName,
        name: route.params.name,
      }),
      edit: (route) => route.query,
    },
  },
  {
    path: '/list/:schemaName/:pageTitle?',
    name: 'ListView',
    components: {
      default: ListView,
      edit: QuickEditForm,
    },
    props: {
      default: (route) => {
        const { schemaName } = route.params;
        const pageTitle = route.params.pageTitle ?? '';

        const filters = {};
        const filterString = route.query.filters;
        if (typeof filterString === 'string') {
          Object.assign(filters, JSON.parse(filterString));
        }

        return {
          schemaName,
          filters,
          pageTitle,
        };
      },
      edit: (route) => route.query,
    },
  },
  {
    path: '/print/:schemaName/:name',
    name: 'PrintView',
    component: PrintView,
    props: true,
  },
  {
    path: '/report-print/:reportName',
    name: 'ReportPrintView',
    component: ReportPrintView,
    props: true,
  },
  {
    path: '/report/:reportClassName',
    name: 'Report',
    component: Report,
    props: true,
  },
  {
    path: '/chart-of-accounts',
    name: 'Chart Of Accounts',
    components: {
      default: ChartOfAccounts,
      edit: QuickEditForm,
    },
    props: {
      default: true,
      edit: (route) => route.query,
    },
  },
  {
    path: '/import-wizard',
    name: 'Import Wizard',
    component: ImportWizard,
  },
  {
    path: '/template-builder/:name',
    name: 'Template Builder',
    component: TemplateBuilder,
    props: true,
  },
  {
    path: '/customize-form',
    name: 'Customize Form',
    component: CustomizeForm,
  },
  {
    path: '/settings',
    name: 'Settings',
    components: {
      default: Settings,
      edit: QuickEditForm,
    },
    props: {
      default: true,
      edit: (route) => route.query,
    },
  },
  {
    path: '/pos',
    name: 'Point of Sale',
    components: {
      default: POS,
      edit: QuickEditForm,
    },
    props: {
      default: true,
      edit: (route) => route.query,
    },
    meta: { requiresAuth: true }
  },
  {
    path: '/user-management',
    name: 'User Management',
    component: UserManagement,
    meta: { requiresAuth: true, requiresAdmin: true }
  },
  {
    path: '/test',
    name: 'Test Page',
    component: TestPage,
    meta: { requiresAuth: false }
  },
  {
    path: '/reset-password',
    name: 'Reset Password',
    component: ResetPassword,
    meta: { requiresAuth: false }
  },
  {
    path: '/reset-admin',
    name: 'Reset Admin Password',
    component: ResetAdminPassword,
    meta: { requiresAuth: false }
  },
  {
    path: '/fix-user',
    name: 'Fix User Password',
    component: FixUserPassword,
    meta: { requiresAuth: false }
  },
  {
    path: '/fix-active',
    name: 'Fix User Active',
    component: FixUserActive,
    meta: { requiresAuth: false }
  },
  {
    path: '/fix-role',
    name: 'Fix User Role',
    component: FixUserRole,
    meta: { requiresAuth: false }
  },
  {
    path: '/license-activation',
    name: 'License Activation',
    component: LicenseActivation,
    meta: { requiresAuth: false }
  },
  {
    path: '/license-info',
    name: 'License Info',
    component: LicenseInfo,
    meta: { requiresAuth: true }
  },
  {
    path: '/license-generator',
    name: 'License Generator',
    component: LicenseGenerator,
    meta: { requiresAuth: true, requiresAdmin: true }
  },
];

const router = createRouter({ routes, history: createWebHistory() });

// Navigation guard for authentication and license
router.beforeEach(async (to, from, next) => {
  console.log('Navigation to:', to.path);

  // تحقق خاص للمطور - تجاوز فحص الترخيص للمطور
  const isDeveloper = () => {
    try {
      // فحص متغيرات البيئة أو ملف خاص للمطور
      const isDev = process.env.NODE_ENV === 'development' ||
                   localStorage.getItem('DEVELOPER_MODE') === 'true' ||
                   window.location.hostname === 'localhost' ||
                   window.location.hostname === '127.0.0.1';
      return isDev;
    } catch (error) {
      return false;
    }
  };

  // التحقق من الترخيص أولاً (ما عدا صفحات التفعيل وتسجيل الدخول والمطور)
  if (to.path !== '/license-activation' && to.path !== '/login' && !isDeveloper()) {
    try {
      const licenseManager = LicenseManager.getInstance();
      const licenseInfo = await licenseManager.getLicenseInfo();

      if (!licenseInfo.isValid) {
        console.log('License invalid, redirecting to license activation');
        next('/license-activation');
        return;
      }

      // التحقق الدوري من الترخيص
      await licenseManager.periodicCheck();
    } catch (error) {
      console.error('Error checking license:', error);
      next('/license-activation');
      return;
    }
  }

  const requiresAuth = to.matched.some(record => record.meta.requiresAuth !== false);
  const requiresAdmin = to.matched.some(record => record.meta.requiresAdmin);
  const currentUser = localStorage.getItem('currentUser');

  // If route requires authentication and user is not logged in
  if (requiresAuth && !currentUser) {
    console.log('Authentication required, redirecting to login');
    // تغيير الشاشة النشطة إلى Login
    window.location.href = '/login';
    return;
  }

  // If route requires admin role, check user role
  if (requiresAdmin && currentUser) {
    // Get user role from localStorage
    const userRole = localStorage.getItem('userRole');

    if (userRole === 'Admin') {
      console.log('Admin access granted');
      next();
    } else {
      console.log('Admin access denied, redirecting to dashboard');
      // Redirect non-admin users to dashboard
      next('/');
    }
    return;
  }

  // If user is logged in and tries to access login page
  if (to.path === '/login' && currentUser) {
    console.log('User already logged in, redirecting to dashboard');
    next('/');
    return;
  }

  next();
});

router.afterEach(({ fullPath }) => {
  const state = history.state as HistoryState;
  historyState.forward = !!state.forward;
  historyState.back = !!state.back;

  if (fullPath.includes('index.html')) {
    return;
  }

  localStorage.setItem('lastRoute', fullPath);
});

export default router;
