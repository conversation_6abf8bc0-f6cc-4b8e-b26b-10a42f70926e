import { Doc } from 'fyo/model/doc';
import { ValidationMap } from 'fyo/model/types';
import { validateEmail } from 'src/utils/validation';
import { simpleHash } from 'src/utils/crypto';

export class User extends Doc {
  email?: string;
  password?: string;
  fullName?: string;
  role?: 'Admin' | 'User';
  isActive?: number;
  hasAllPermissions?: number;

  validations: ValidationMap = {
    email: validateEmail,
  };

  // تحويل قيمة isActive إلى قيمة منطقية (boolean)
  get isActiveBoolean(): boolean {
    return this.isActive === 1;
  }

  // تحويل قيمة hasAllPermissions إلى قيمة منطقية (boolean)
  get hasAllPermissionsBoolean(): boolean {
    return this.hasAllPermissions === 1;
  }

  // التحقق من صلاحيات المستخدم للوحدة المحددة
  async hasPermissionFor(module: string): Promise<boolean> {
    // إذا كان المستخدم مديرًا أو لديه جميع الصلاحيات، فلديه صلاحية للوحدة
    if (this.role === 'Admin' || this.hasAllPermissionsBoolean) {
      return true;
    }

    // التحقق من وجود صلاحية للوحدة المحددة
    try {
      const fyo = this.fyo;
      const filters = {
        user: this.name,
        module: module,
        isActive: 1
      };

      const permissions = await fyo.db.getAllRaw('UserPermission', { filters });
      return permissions.length > 0;
    } catch (error) {
      console.error('Error checking permissions:', error);
      return false;
    }
  }

  // تعيين قيمة isActive كرقم صحيح
  setActive(active: boolean | number | string): void {
    console.log('Setting active state:', active);
    console.log('Active type:', typeof active);

    let newValue = 0;

    if (typeof active === 'boolean') {
      newValue = active ? 1 : 0;
    } else if (typeof active === 'string') {
      newValue = active === '1' || active === 'true' ? 1 : 0;
    } else if (typeof active === 'number') {
      newValue = active > 0 ? 1 : 0;
    } else {
      newValue = active ? 1 : 0;
    }

    console.log('Setting isActive to:', newValue);
    this.isActive = newValue;
  }

  async beforeInsert() {
    console.log('Before insert - Initial values:', {
      password: this.password ? '[REDACTED]' : undefined,
      isActive: this.isActive,
      isActiveType: typeof this.isActive
    });

    if (this.password) {
      this.password = this.hashPassword(this.password);
    }

    // تأكد من أن isActive هو رقم صحيح
    if (this.isActive === undefined || this.isActive === null) {
      console.log('isActive is undefined or null, setting to 1');
      this.isActive = 1; // نشط افتراضيًا
    } else {
      console.log('Setting isActive using setActive');
      this.setActive(this.isActive);
    }

    console.log('Before insert - Final values:', {
      password: this.password ? '[REDACTED]' : undefined,
      isActive: this.isActive,
      isActiveType: typeof this.isActive
    });
  }

  async beforeUpdate() {
    console.log('Before update - Initial values:', {
      password: this.password ? '[REDACTED]' : undefined,
      isActive: this.isActive,
      isActiveType: typeof this.isActive
    });

    // Only hash the password if it has changed
    if (this.password && this.password !== this._dirty.password) {
      this.password = this.hashPassword(this.password);
    }

    // تأكد من أن isActive هو رقم صحيح
    if (this.isActive !== undefined && this.isActive !== null) {
      console.log('Setting isActive using setActive');
      this.setActive(this.isActive);
    }

    console.log('Before update - Final values:', {
      password: this.password ? '[REDACTED]' : undefined,
      isActive: this.isActive,
      isActiveType: typeof this.isActive
    });
  }

  hashPassword(password: string): string {
    // استخدام دالة التشفير من ملف crypto.ts
    console.log('Hashing password:', password);
    const hashed = simpleHash(password);
    console.log('Hashed password:', hashed);
    return hashed;
  }

  verifyPassword(password: string): boolean {
    console.log('Verifying password:', password);
    console.log('Stored password:', this.password);

    const hashedPassword = this.hashPassword(password);
    console.log('Hashed input password:', hashedPassword);

    const isMatch = hashedPassword === this.password;
    console.log('Password match:', isMatch);

    return isMatch;
  }
}
