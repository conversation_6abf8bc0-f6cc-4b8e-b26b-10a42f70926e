<main class="h-auto w-auto m-2.5 p-0.5 pb-5 bg-white border border-gray-500">
  <!-- Invoice Header -->
  <header class="pt-4 flex flex-col items-center">
    <!-- Company Logo & Name -->
    <img
      v-if="print.displayLogo"
      class="h-18 py-1 w-full object-contain"
      :src="print.logo"
    />
    <h2 class="font-semibold text-xl" :style="{ color: print.color }">
      {{ print.companyName }}
    </h2>

    <!-- Company Address -->
    <p v-if="print.address" class="w-11/12 text-lg text-center">
      {{ print.links.address.addressDisplay }}
    </p>
    <!-- Company Contacts -->
    <p class="mt-1 text-base">{{ print.phone }}</p>
    <p class="text-base">{{ print.email }}</p>
  </header>
  <!-- Invoice Details -->

  <section class="mt-1 flex flex-col justify-between items-center">
    <h4 class="font-semibold text-xl">Invoice</h4>
  </section>

  <div class="w-auto text-xs grid grid-cols-7 grid-rows-3 p-2">
    <div class="col-span-2 font-bold"><p>Invoice No</p></div>
    <div class="col-start-3">:</div>
    <div class="col-span-4 col-start-4">{{ doc.name }}</div>
    <div class="col-span-2 row-start-2 font-bold">Customer</div>
    <div class="col-start-3 row-start-2">:</div>
    <div class="col-span-4 col-start-4 row-start-2">{{ doc.party }}</div>
    <div class="col-span-2 col-start-1 row-start-3 font-bold">Date</div>
    <div class="col-span-2 col-start-3 row-start-3">:</div>
    <div class="col-span-4 col-start-4 row-start-3">{{doc.date}}</div>
  </div>

  <!-- Items Table -->
  <section class="mt-1 mx-2 text-sm">
    <!-- Heading Row -->
    <section class="font-bold w-auto flex border-b border-t border-black">
      <div class="py-1 w-4">{{ t`SI` }}</div>
      <div class="py-1 w-2/5">{{ t`Item` }}</div>
      <div class="py-1 w-1/12">{{ t`Qty` }}</div>
      <div class="py-1 text-right w-3/12">{{ t`Price` }}</div>
      <div class="py-1 text-right w-3/12">{{ t`Amount` }}</div>
    </section>

    <!-- Body Rows -->
    <section
      class="flex py-1 text-gray-900 w-auto"
      v-for="(row,index) in doc.items"
      :key="row.name"
    >
      <div class="w-4 py-1">{{ index + 1 }}</div>
      <div class="w-2/5 py-1">{{ row.item }}</div>
      <div class="w-1/12 py-1 text-left">{{ row.quantity }}</div>
      <div class="w-3/12 text-right py-1">{{ row.rate }}</div>
      <div class="w-3/12 text-right py-1">{{ row.amount }}</div>
    </section>

    <!-- Totaled Amounts -->
    <section class="flex justify-between border-b border-t border-black py-1">
      <p>Total</p>
      <p>{{ doc.netTotal}}</p>
    </section>

    <!-- Grand Total -->
    <section class="flex justify-between pt-3">
      <p class="font-semibold text-lg">Net Total To Pay</p>
      <p class="font-semibold text-xl">{{ doc.grandTotal }}</p>
    </section>
  </section>

  <!-- Tax Breakdown -->
  <section v-if="doc.taxes.length" class="mx-2">
    <p
      class="
        flex
        justify-center
        font-semibold
        border-t border-black
        text-lg
        mt-2
      "
    >
      Tax Summary
    </p>

    <div class="flex justify-between pl-5 text-base">
      <p>Total Ex.Tax</p>
      <p>{{doc.subTotal}}</p>
    </div>

    <div
      class="flex justify-between pl-5 text-base"
      v-for="tax in doc.taxes"
      :key="tax.name"
    >
      <p>{{ tax.account }}</p>
      <p>{{ tax.amount }}</p>
    </div>
  </section>

  <!-- Discounts & Payments -->
  <section class="mx-1 pt-2 text-sm mr-1 border-b border-black">
    <div class="grid grid-cols-3 grid-rows-1">
      <div>{{ t`Payment` }}</div>
      <div class="text-right">{{ t`Tendered` }}</div>
      <div class="text-right">{{ t`Balance` }}</div>
    </div>

    <div
      class="w-auto grid grid-cols-3 grid-rows-1 border-t border-black text-xs"
    >
      <div>{{ t`Discount` }}</div>
      <div class="text-right font-medium">
        {{ doc.totalDiscount ? doc.totalDiscount : '00.00'}}
      </div>
    </div>

    <div v-for="(row,index) in doc.paymentDetails">
      <div class="w-auto grid grid-cols-3 border-black text-xs">
        <div class="row-start-2">{{ row.paymentMethod }}</div>
        <div class="row-start-2 font-medium text-right">{{ row.amount }}</div>
        <div class="row-start-2 font-medium text-right">
          {{ row.outstandingAmount }}
        </div>
      </div>
    </div>
  </section>

  <div class="w-full mt-3 flex pb-5 flex-col justify-center items-center">
    <p class="pt-1 text-lg">***** Thank You Visit Again *****</p>
  </div>
</main>
