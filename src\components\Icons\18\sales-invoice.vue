<template>
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18">
    <path
      fill="#36D3B1"
      d="M14.25,0 L0.75,0 C0.335786438,-2.53632657e-17 0,0.335786438 0,0.75 L0,17.901 L3,15.9 L5.25,17.4 L7.5,15.9 L9.75,17.4 L12,15.9 L15,17.8995 L15,0.75 C15,0.335786438 14.6642136,2.53632657e-17 14.25,0 Z M8.25,11.25 L3,11.25 L3,9.75 L8.25,9.75 L8.25,11.25 Z M7.5,7.5 C6.25735931,7.5 5.25,6.49264069 5.25,5.25 C5.25,4.00735931 6.25735931,3 7.5,3 C8.74264069,3 9.75,4.00735931 9.75,5.25 C9.75,5.8467371 9.51294711,6.41903341 9.09099026,6.84099026 C8.66903341,7.26294711 8.0967371,7.5 7.5,7.5 Z M12,11.25 L9.75,11.25 L9.75,9.75 L12,9.75 L12,11.25 Z"
      transform="translate(1.5)"
    />
  </svg>
</template>
<script>
import Base from '../base.vue';
export default {
  extends: Base,
};
</script>
