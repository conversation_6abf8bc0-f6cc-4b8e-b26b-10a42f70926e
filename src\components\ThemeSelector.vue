<template>
  <div class="theme-selector">
    <!-- زر فتح منتقي الثيم -->
    <button
      @click="showThemeModal = true"
      class="theme-toggle-btn"
      :title="t`تغيير الثيم`"
    >
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
              d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v6a2 2 0 002 2h4a2 2 0 002-2V5z"/>
      </svg>
    </button>

    <!-- نافذة اختيار الثيم -->
    <div v-if="showThemeModal" class="theme-modal-overlay" @click="showThemeModal = false">
      <div class="theme-modal" @click.stop>
        <div class="theme-modal-header">
          <h3 class="theme-modal-title">{{ t`اختيار الثيم` }}</h3>
          <button @click="showThemeModal = false" class="theme-modal-close">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>
        </div>

        <div class="theme-modal-content">
          <div class="theme-grid">
            <div
              v-for="(theme, key) in availableThemes"
              :key="key"
              @click="selectTheme(key)"
              :class="[
                'theme-card',
                { 'theme-card-active': currentTheme === key }
              ]"
            >
              <!-- معاينة الثيم -->
              <div class="theme-preview" :data-theme="key">
                <div class="theme-preview-sidebar"></div>
                <div class="theme-preview-content">
                  <div class="theme-preview-header"></div>
                  <div class="theme-preview-body">
                    <div class="theme-preview-card"></div>
                    <div class="theme-preview-card"></div>
                  </div>
                </div>
              </div>

              <!-- معلومات الثيم -->
              <div class="theme-info">
                <h4 class="theme-name">{{ theme.name }}</h4>
                <p class="theme-description">
                  {{ getThemeDescription(key) }}
                </p>
              </div>

              <!-- مؤشر الثيم النشط -->
              <div v-if="currentTheme === key" class="theme-active-indicator">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
              </div>
            </div>
          </div>

          <!-- خيارات إضافية -->
          <div class="theme-options">
            <div class="theme-option">
              <label class="theme-option-label">
                <input
                  type="checkbox"
                  v-model="autoTheme"
                  @change="toggleAutoTheme"
                  class="theme-option-checkbox"
                />
                <span>{{ t`تغيير تلقائي حسب الوقت` }}</span>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { themes, applyTheme, getCurrentTheme } from 'src/utils/theme';

export default defineComponent({
  name: 'ThemeSelector',
  data() {
    return {
      showThemeModal: false,
      currentTheme: getCurrentTheme(),
      availableThemes: themes,
      autoTheme: localStorage.getItem('autoTheme') === 'true',
    };
  },
  methods: {
    selectTheme(themeKey: string) {
      this.currentTheme = themeKey;
      applyTheme(themeKey);
      this.showThemeModal = false;
      
      // إشعار بتغيير الثيم
      this.$emit('theme-changed', themeKey);
    },

    getThemeDescription(themeKey: string): string {
      const descriptions = {
        modern: 'ثيم عصري بألوان زرقاء هادئة',
        dark: 'ثيم مظلم احترافي للعمل الليلي',
        ocean: 'ثيم بألوان البحر المنعشة',
        forest: 'ثيم بألوان الطبيعة الخضراء',
        sunset: 'ثيم بألوان الغروب الدافئة'
      };
      return descriptions[themeKey] || 'ثيم مخصص';
    },

    toggleAutoTheme() {
      localStorage.setItem('autoTheme', this.autoTheme.toString());
      
      if (this.autoTheme) {
        this.setupAutoTheme();
      } else {
        this.clearAutoTheme();
      }
    },

    setupAutoTheme() {
      const hour = new Date().getHours();
      const isDayTime = hour >= 6 && hour < 18;
      
      if (isDayTime) {
        this.selectTheme('modern');
      } else {
        this.selectTheme('dark');
      }

      // تحديث كل ساعة
      setInterval(() => {
        if (this.autoTheme) {
          const currentHour = new Date().getHours();
          const isCurrentlyDay = currentHour >= 6 && currentHour < 18;
          
          if (isCurrentlyDay && this.currentTheme === 'dark') {
            this.selectTheme('modern');
          } else if (!isCurrentlyDay && this.currentTheme !== 'dark') {
            this.selectTheme('dark');
          }
        }
      }, 3600000); // كل ساعة
    },

    clearAutoTheme() {
      // إيقاف التغيير التلقائي
    }
  },

  mounted() {
    if (this.autoTheme) {
      this.setupAutoTheme();
    }

    // الاستماع لتغيير الثيم من مصادر أخرى
    window.addEventListener('theme-changed', (event: any) => {
      this.currentTheme = event.detail.theme;
    });
  }
});
</script>

<style scoped>
.theme-toggle-btn {
  @apply p-2 rounded-lg bg-white dark:bg-gray-800 shadow-md hover:shadow-lg;
  @apply text-gray-600 dark:text-gray-300 hover:text-blue-600;
  @apply transition-all duration-200 transform hover:scale-105;
}

.theme-modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
  @apply backdrop-blur-sm;
}

.theme-modal {
  @apply bg-white dark:bg-gray-800 rounded-2xl shadow-2xl;
  @apply w-full max-w-4xl max-h-[90vh] overflow-hidden;
  @apply m-4;
}

.theme-modal-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700;
}

.theme-modal-title {
  @apply text-xl font-semibold text-gray-900 dark:text-white;
}

.theme-modal-close {
  @apply p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700;
  @apply text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200;
  @apply transition-colors duration-200;
}

.theme-modal-content {
  @apply p-6 overflow-y-auto max-h-[calc(90vh-120px)];
}

.theme-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6;
}

.theme-card {
  @apply relative bg-gray-50 dark:bg-gray-700 rounded-xl p-4 cursor-pointer;
  @apply border-2 border-transparent hover:border-blue-300;
  @apply transition-all duration-200 transform hover:scale-105;
}

.theme-card-active {
  @apply border-blue-500 bg-blue-50 dark:bg-blue-900;
}

.theme-preview {
  @apply w-full h-24 rounded-lg overflow-hidden mb-3 flex;
  @apply border border-gray-200 dark:border-gray-600;
}

.theme-preview-sidebar {
  @apply w-1/4 bg-sidebar-800;
}

.theme-preview-content {
  @apply flex-1 bg-content-50 p-2;
}

.theme-preview-header {
  @apply w-full h-2 bg-gray-300 rounded mb-2;
}

.theme-preview-body {
  @apply space-y-1;
}

.theme-preview-card {
  @apply w-full h-2 bg-gray-200 rounded;
}

.theme-info {
  @apply text-center;
}

.theme-name {
  @apply font-medium text-gray-900 dark:text-white mb-1;
}

.theme-description {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.theme-active-indicator {
  @apply absolute top-2 right-2 w-6 h-6 bg-blue-500 text-white rounded-full;
  @apply flex items-center justify-center;
}

.theme-options {
  @apply border-t border-gray-200 dark:border-gray-700 pt-6;
}

.theme-option {
  @apply flex items-center justify-center;
}

.theme-option-label {
  @apply flex items-center space-x-2 space-x-reverse cursor-pointer;
  @apply text-gray-700 dark:text-gray-300;
}

.theme-option-checkbox {
  @apply w-4 h-4 text-blue-600 border-gray-300 rounded;
  @apply focus:ring-blue-500 focus:ring-2;
}

/* تخصيص معاينة الثيمات */
[data-theme="modern"] .theme-preview-sidebar {
  @apply bg-slate-800;
}

[data-theme="modern"] .theme-preview-content {
  @apply bg-slate-50;
}

[data-theme="dark"] .theme-preview-sidebar {
  @apply bg-gray-900;
}

[data-theme="dark"] .theme-preview-content {
  @apply bg-gray-800;
}

[data-theme="ocean"] .theme-preview-sidebar {
  @apply bg-blue-900;
}

[data-theme="ocean"] .theme-preview-content {
  @apply bg-blue-50;
}

[data-theme="forest"] .theme-preview-sidebar {
  @apply bg-green-800;
}

[data-theme="forest"] .theme-preview-content {
  @apply bg-green-50;
}

[data-theme="sunset"] .theme-preview-sidebar {
  @apply bg-orange-800;
}

[data-theme="sunset"] .theme-preview-content {
  @apply bg-orange-50;
}
</style>
