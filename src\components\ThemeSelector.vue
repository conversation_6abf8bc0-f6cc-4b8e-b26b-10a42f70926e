<template>
  <div class="theme-selector">
    <!-- زر فتح منتقي الثيم -->
    <button
      @click="showThemeModal = true"
      class="theme-toggle-btn"
      :title="t`تغيير الثيم`"
    >
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
              d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v6a2 2 0 002 2h4a2 2 0 002-2V5z"/>
      </svg>
    </button>

    <!-- نافذة اختيار الثيم -->
    <div v-if="showThemeModal" class="theme-modal-overlay" @click="showThemeModal = false">
      <div class="theme-modal" @click.stop>
        <div class="theme-modal-header">
          <h3 class="theme-modal-title">{{ t`اختيار الثيم` }}</h3>
          <button @click="showThemeModal = false" class="theme-modal-close">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>
        </div>

        <div class="theme-modal-content">
          <div class="theme-grid">
            <div
              v-for="(theme, key) in availableThemes"
              :key="key"
              @click="selectTheme(key)"
              :class="[
                'theme-card',
                { 'theme-card-active': currentTheme === key }
              ]"
            >
              <!-- معاينة الثيم -->
              <div class="theme-preview" :data-theme="key">
                <div class="theme-preview-sidebar"></div>
                <div class="theme-preview-content">
                  <div class="theme-preview-header"></div>
                  <div class="theme-preview-body">
                    <div class="theme-preview-card"></div>
                    <div class="theme-preview-card"></div>
                  </div>
                </div>
              </div>

              <!-- معلومات الثيم -->
              <div class="theme-info">
                <h4 class="theme-name">{{ theme.name }}</h4>
                <p class="theme-description">
                  {{ getThemeDescription(key) }}
                </p>
              </div>

              <!-- مؤشر الثيم النشط -->
              <div v-if="currentTheme === key" class="theme-active-indicator">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
              </div>
            </div>
          </div>

          <!-- خيارات إضافية -->
          <div class="theme-options">
            <div class="theme-option">
              <label class="theme-option-label">
                <input
                  type="checkbox"
                  v-model="autoTheme"
                  @change="toggleAutoTheme"
                  class="theme-option-checkbox"
                />
                <span>{{ t`تغيير تلقائي حسب الوقت` }}</span>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { themes, applyTheme, getCurrentTheme } from 'src/utils/theme';

export default defineComponent({
  name: 'ThemeSelector',
  data() {
    return {
      showThemeModal: false,
      currentTheme: getCurrentTheme(),
      availableThemes: themes,
      autoTheme: localStorage.getItem('autoTheme') === 'true',
    };
  },
  methods: {
    selectTheme(themeKey: string) {
      this.currentTheme = themeKey;
      applyTheme(themeKey);
      this.showThemeModal = false;
      
      // إشعار بتغيير الثيم
      this.$emit('theme-changed', themeKey);
    },

    getThemeDescription(themeKey: string): string {
      const descriptions = {
        modern: 'ثيم عصري بألوان زرقاء هادئة',
        dark: 'ثيم مظلم احترافي للعمل الليلي',
        ocean: 'ثيم بألوان البحر المنعشة',
        forest: 'ثيم بألوان الطبيعة الخضراء',
        sunset: 'ثيم بألوان الغروب الدافئة',
        skyBlue: 'ثيم سماوي هادئ ومريح للعين',
        lightBlue: 'ثيم أزرق فاتح وناعم'
      };
      return descriptions[themeKey] || 'ثيم مخصص';
    },

    toggleAutoTheme() {
      localStorage.setItem('autoTheme', this.autoTheme.toString());
      
      if (this.autoTheme) {
        this.setupAutoTheme();
      } else {
        this.clearAutoTheme();
      }
    },

    setupAutoTheme() {
      const hour = new Date().getHours();
      const isDayTime = hour >= 6 && hour < 18;
      
      if (isDayTime) {
        this.selectTheme('modern');
      } else {
        this.selectTheme('dark');
      }

      // تحديث كل ساعة
      setInterval(() => {
        if (this.autoTheme) {
          const currentHour = new Date().getHours();
          const isCurrentlyDay = currentHour >= 6 && currentHour < 18;
          
          if (isCurrentlyDay && this.currentTheme === 'dark') {
            this.selectTheme('modern');
          } else if (!isCurrentlyDay && this.currentTheme !== 'dark') {
            this.selectTheme('dark');
          }
        }
      }, 3600000); // كل ساعة
    },

    clearAutoTheme() {
      // إيقاف التغيير التلقائي
    }
  },

  mounted() {
    if (this.autoTheme) {
      this.setupAutoTheme();
    }

    // الاستماع لتغيير الثيم من مصادر أخرى
    window.addEventListener('theme-changed', (event: any) => {
      this.currentTheme = event.detail.theme;
    });
  }
});
</script>

<style scoped>
.theme-toggle-btn {
  padding: 0.5rem;
  border-radius: 0.5rem;
  background-color: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  color: #4B5563;
  transition: all 0.2s ease-in-out;
}

.theme-toggle-btn:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  color: #2563EB;
  transform: scale(1.05);
}

.dark .theme-toggle-btn {
  background-color: #1F2937;
  color: #D1D5DB;
}

.theme-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
  backdrop-filter: blur(4px);
}

.theme-modal {
  background-color: white;
  border-radius: 1rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  width: 100%;
  max-width: 56rem;
  max-height: 90vh;
  overflow: hidden;
  margin: 1rem;
}

.dark .theme-modal {
  background-color: #1F2937;
}

.theme-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid #E5E7EB;
}

.dark .theme-modal-header {
  border-bottom-color: #374151;
}

.theme-modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
}

.dark .theme-modal-title {
  color: white;
}

.theme-modal-close {
  padding: 0.5rem;
  border-radius: 0.5rem;
  color: #6B7280;
  transition: all 0.2s;
}

.theme-modal-close:hover {
  background-color: #F3F4F6;
  color: #374151;
}

.dark .theme-modal-close {
  color: #9CA3AF;
}

.dark .theme-modal-close:hover {
  background-color: #374151;
  color: #E5E7EB;
}

.theme-modal-content {
  padding: 1.5rem;
  overflow-y: auto;
  max-height: calc(90vh - 120px);
}

.theme-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.theme-card {
  position: relative;
  background-color: #F9FAFB;
  border-radius: 0.75rem;
  padding: 1rem;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.2s;
}

.theme-card:hover {
  border-color: #93C5FD;
  transform: scale(1.02);
}

.theme-card-active {
  border-color: #3B82F6;
  background-color: #EFF6FF;
}

.dark .theme-card {
  background-color: #374151;
}

.dark .theme-card-active {
  background-color: #1E3A8A;
}

.theme-preview {
  width: 100%;
  height: 6rem;
  border-radius: 0.5rem;
  overflow: hidden;
  margin-bottom: 0.75rem;
  display: flex;
  border: 1px solid #E5E7EB;
}

.dark .theme-preview {
  border-color: #4B5563;
}

.theme-preview-sidebar {
  width: 25%;
}

.theme-preview-content {
  flex: 1;
  padding: 0.5rem;
}

.theme-preview-header {
  width: 100%;
  height: 0.5rem;
  background-color: #D1D5DB;
  border-radius: 0.25rem;
  margin-bottom: 0.5rem;
}

.theme-preview-body {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.theme-preview-card {
  width: 100%;
  height: 0.5rem;
  background-color: #E5E7EB;
  border-radius: 0.25rem;
}

.theme-info {
  text-align: center;
}

.theme-name {
  font-weight: 500;
  color: #111827;
  margin-bottom: 0.25rem;
}

.dark .theme-name {
  color: white;
}

.theme-description {
  font-size: 0.875rem;
  color: #6B7280;
}

.dark .theme-description {
  color: #9CA3AF;
}

.theme-active-indicator {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  width: 1.5rem;
  height: 1.5rem;
  background-color: #3B82F6;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.theme-options {
  border-top: 1px solid #E5E7EB;
  padding-top: 1.5rem;
}

.dark .theme-options {
  border-top-color: #374151;
}

.theme-option {
  display: flex;
  align-items: center;
  justify-content: center;
}

.theme-option-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  color: #374151;
}

.dark .theme-option-label {
  color: #D1D5DB;
}

.theme-option-checkbox {
  width: 1rem;
  height: 1rem;
  color: #2563EB;
  border-color: #D1D5DB;
  border-radius: 0.25rem;
}

/* تخصيص معاينة الثيمات */
[data-theme="modern"] .theme-preview-sidebar {
  background-color: #1E293B;
}

[data-theme="modern"] .theme-preview-content {
  background-color: #F8FAFC;
}

[data-theme="dark"] .theme-preview-sidebar {
  background-color: #111827;
}

[data-theme="dark"] .theme-preview-content {
  background-color: #1F2937;
}

[data-theme="ocean"] .theme-preview-sidebar {
  background-color: #1E3A8A;
}

[data-theme="ocean"] .theme-preview-content {
  background-color: #EFF6FF;
}

[data-theme="forest"] .theme-preview-sidebar {
  background-color: #166534;
}

[data-theme="forest"] .theme-preview-content {
  background-color: #F0FDF4;
}

[data-theme="sunset"] .theme-preview-sidebar {
  background-color: #C2410C;
}

[data-theme="sunset"] .theme-preview-content {
  background-color: #FFF7ED;
}

[data-theme="skyBlue"] .theme-preview-sidebar {
  background-color: #075985;
}

[data-theme="skyBlue"] .theme-preview-content {
  background-color: #F0F9FF;
}

[data-theme="lightBlue"] .theme-preview-sidebar {
  background-color: #0369A1;
}

[data-theme="lightBlue"] .theme-preview-content {
  background-color: #F0F9FF;
}
</style>
