import { t } from 'fyo';
import { routeFilters } from 'src/utils/filters';
import { fyo } from '../initFyo';
import { SidebarConfig, SidebarItem, SidebarRoot } from './types';
import { LicenseManager } from './licenseManager';

export async function getSidebarConfig(): Promise<SidebarConfig> {
  const sideBar = getCompleteSidebar();
  return await getFilteredSidebar(sideBar);
}

async function checkUserPermission(module: string): Promise<boolean> {
  try {
    const currentUser = localStorage.getItem('currentUser');
    if (!currentUser) return false;

    // التحقق من الترخيص أولاً
    const licenseManager = LicenseManager.getInstance();
    const isModuleAllowed = await licenseManager.isModuleAllowed(module);
    if (!isModuleAllowed) {
      console.log(`Module ${module} not allowed by license`);
      return false;
    }

    // إذا كان المستخدم هو admin، فلديه جميع الصلاحيات
    if (currentUser === 'admin') return true;

    // التحقق من دور المستخدم
    const userRole = localStorage.getItem('userRole');
    if (userRole === 'Admin') return true;

    // محاولة الحصول على صلاحيات المستخدم من localStorage
    const storedPermissions = localStorage.getItem(`permissions_${currentUser}`);
    if (storedPermissions) {
      try {
        const parsedPermissions = JSON.parse(storedPermissions);

        // التحقق من وجود الوحدة في قائمة الصلاحيات
        if (parsedPermissions.modules && Array.isArray(parsedPermissions.modules)) {
          const hasPermission = parsedPermissions.modules.includes(module);
          console.log(`User ${currentUser} permission for module ${module}: ${hasPermission}`);
          return hasPermission;
        }
      } catch (parseError) {
        console.error('Error parsing stored permissions:', parseError);
      }
    }

    // محاولة الحصول على قيمة hasAllPermissions من localStorage
    const hasAllPermissions = localStorage.getItem(`hasAllPermissions_${currentUser}`);
    if (hasAllPermissions === '1' || hasAllPermissions === 'true') {
      console.log(`User ${currentUser} has all permissions`);
      return true;
    }

    // إذا وصلنا إلى هنا، فلا توجد صلاحية
    console.log(`User ${currentUser} has no permission for module ${module}`);
    return false;
  } catch (error) {
    console.error('Error checking user permission:', error);
    return false;
  }
}

async function getFilteredSidebar(sideBar: SidebarConfig): Promise<SidebarConfig> {
  const filteredSidebar = [];

  for (const root of sideBar) {
    // التحقق من صلاحيات المستخدم للوحدة
    if (root.module && !(await checkUserPermission(root.module))) {
      continue;
    }

    // تصفية العناصر الفرعية
    if (root.items) {
      const filteredItems = [];

      for (const item of root.items) {
        if (item.hidden !== undefined && item.hidden()) {
          continue;
        }

        filteredItems.push(item);
      }

      root.items = filteredItems;
    }

    // التحقق من الإخفاء
    if (root.hidden !== undefined && root.hidden()) {
      continue;
    }

    filteredSidebar.push(root);
  }

  return filteredSidebar;
}

function getRegionalSidebar(): SidebarRoot[] {
  const hasGstin = !!fyo.singles?.AccountingSettings?.gstin;
  if (!hasGstin) {
    return [];
  }

  return [
    {
      label: t`GST`,
      name: 'gst',
      icon: 'gst',
      route: '/report/GSTR1',
      items: [
        {
          label: t`GSTR1`,
          name: 'gstr1',
          route: '/report/GSTR1',
        },
        {
          label: t`GSTR2`,
          name: 'gstr2',
          route: '/report/GSTR2',
        },
      ],
    },
  ];
}

function getInventorySidebar(): SidebarRoot[] {
  const hasInventory = !!fyo.singles.AccountingSettings?.enableInventory;
  if (!hasInventory) {
    return [];
  }

  return [
    {
      label: t`Inventory`,
      name: 'inventory',
      icon: 'inventory',
      iconSize: '18',
      route: '/list/StockMovement',
      items: [
        {
          label: t`Stock Movement`,
          name: 'stock-movement',
          route: '/list/StockMovement',
          schemaName: 'StockMovement',
        },
        {
          label: t`Shipment`,
          name: 'shipment',
          route: '/list/Shipment',
          schemaName: 'Shipment',
        },
        {
          label: t`Purchase Receipt`,
          name: 'purchase-receipt',
          route: '/list/PurchaseReceipt',
          schemaName: 'PurchaseReceipt',
        },
        {
          label: t`Stock Ledger`,
          name: 'stock-ledger',
          route: '/report/StockLedger',
        },
        {
          label: t`Stock Balance`,
          name: 'stock-balance',
          route: '/report/StockBalance',
        },
      ],
    },
  ];
}

function getPOSSidebar() {
  return {
    label: t`POS`,
    name: 'pos',
    route: '/pos',
    icon: 'pos',
    hidden: () => !fyo.singles.InventorySettings?.enablePointOfSale,
  };
}

function getReportSidebar() {
  return {
    label: t`Reports`,
    name: 'reports',
    icon: 'reports',
    route: '/report/GeneralLedger',
    items: [
      {
        label: t`General Ledger`,
        name: 'general-ledger',
        route: '/report/GeneralLedger',
      },
      {
        label: t`Profit And Loss`,
        name: 'profit-and-loss',
        route: '/report/ProfitAndLoss',
      },
      {
        label: t`Balance Sheet`,
        name: 'balance-sheet',
        route: '/report/BalanceSheet',
      },
      {
        label: t`Trial Balance`,
        name: 'trial-balance',
        route: '/report/TrialBalance',
      },
    ],
  };
}

function getCompleteSidebar(): SidebarConfig {
  return [
    {
      label: t`Get Started`,
      name: 'get-started',
      route: '/get-started',
      icon: 'general',
      iconSize: '24',
      iconHeight: 5,
      hidden: () => !!fyo.singles.SystemSettings?.hideGetStarted,
    },
    {
      label: t`Dashboard`,
      name: 'dashboard',
      route: '/',
      icon: 'dashboard',
    },
    {
      label: t`Sales`,
      name: 'sales',
      icon: 'sales',
      route: '/list/SalesInvoice',
      module: 'sales',
      items: [
        {
          label: t`Sales Quotes`,
          name: 'sales-quotes',
          route: '/list/SalesQuote',
          schemaName: 'SalesQuote',
        },
        {
          label: t`Sales Invoices`,
          name: 'sales-invoices',
          route: '/list/SalesInvoice',
          schemaName: 'SalesInvoice',
        },
        {
          label: t`Sales Payments`,
          name: 'payments',
          route: `/list/Payment/${t`Sales Payments`}`,
          schemaName: 'Payment',
          filters: routeFilters.SalesPayments,
        },
        {
          label: t`Customers`,
          name: 'customers',
          route: `/list/Party/${t`Customers`}`,
          schemaName: 'Party',
          filters: routeFilters.Customers,
        },
        {
          label: t`Sales Items`,
          name: 'sales-items',
          route: `/list/Item/${t`Sales Items`}`,
          schemaName: 'Item',
          filters: routeFilters.SalesItems,
        },
        {
          label: t`Loyalty Program`,
          name: 'loyalty-program',
          route: '/list/LoyaltyProgram',
          schemaName: 'LoyaltyProgram',
          hidden: () => !fyo.singles.AccountingSettings?.enableLoyaltyProgram,
        },
        {
          label: t`Lead`,
          name: 'lead',
          route: '/list/Lead',
          schemaName: 'Lead',
          hidden: () => !fyo.singles.AccountingSettings?.enableLead,
        },
        {
          label: t`Pricing Rule`,
          name: 'pricing-rule',
          route: '/list/PricingRule',
          schemaName: 'PricingRule',
          hidden: () => !fyo.singles.AccountingSettings?.enablePricingRule,
        },
        {
          label: t`Coupon Code`,
          name: 'coupon-code',
          route: `/list/CouponCode`,
          schemaName: 'CouponCode',
          hidden: () => !fyo.singles.AccountingSettings?.enableCouponCode,
        },
      ] as SidebarItem[],
    },
    {
      label: t`Purchases`,
      name: 'purchases',
      icon: 'purchase',
      route: '/list/PurchaseInvoice',
      module: 'purchases',
      items: [
        {
          label: t`Purchase Invoices`,
          name: 'purchase-invoices',
          route: '/list/PurchaseInvoice',
          schemaName: 'PurchaseInvoice',
        },
        {
          label: t`Purchase Payments`,
          name: 'payments',
          route: `/list/Payment/${t`Purchase Payments`}`,
          schemaName: 'Payment',
          filters: routeFilters.PurchasePayments,
        },
        {
          label: t`Suppliers`,
          name: 'suppliers',
          route: `/list/Party/${t`Suppliers`}`,
          schemaName: 'Party',
          filters: routeFilters.Suppliers,
        },
        {
          label: t`Purchase Items`,
          name: 'purchase-items',
          route: `/list/Item/${t`Purchase Items`}`,
          schemaName: 'Item',
          filters: routeFilters.PurchaseItems,
        },
      ] as SidebarItem[],
    },
    {
      label: t`Common`,
      name: 'common-entries',
      icon: 'common-entries',
      route: '/list/JournalEntry',
      module: 'common',
      items: [
        {
          label: t`Journal Entry`,
          name: 'journal-entry',
          route: '/list/JournalEntry',
          schemaName: 'JournalEntry',
        },
        {
          label: t`Party`,
          name: 'party',
          route: '/list/Party',
          schemaName: 'Party',
          filters: { role: 'Both' },
        },
        {
          label: t`Items`,
          name: 'common-items',
          route: `/list/Item/${t`Items`}`,
          schemaName: 'Item',
          filters: { for: 'Both' },
        },
        {
          label: t`Price List`,
          name: 'price-list',
          route: '/list/PriceList',
          schemaName: 'PriceList',
          hidden: () => !fyo.singles.AccountingSettings?.enablePriceList,
        },
      ] as SidebarItem[],
    },
    { ...getReportSidebar(), module: 'reports' },
    getInventorySidebar().map(item => ({ ...item, module: 'inventory' })),
    { ...getPOSSidebar(), module: 'pos' },
    getRegionalSidebar().map(item => ({ ...item, module: 'gst' })),
    {
      label: t`Setup`,
      name: 'setup',
      icon: 'settings',
      route: '/chart-of-accounts',
      module: 'setup',
      items: [
        {
          label: t`Chart of Accounts`,
          name: 'chart-of-accounts',
          route: '/chart-of-accounts',
        },
        {
          label: t`Tax Templates`,
          name: 'taxes',
          route: '/list/Tax',
          schemaName: 'Tax',
        },
        {
          label: t`Import Wizard`,
          name: 'import-wizard',
          route: '/import-wizard',
        },
        {
          label: t`Print Templates`,
          name: 'print-template',
          route: `/list/PrintTemplate/${t`Print Templates`}`,
        },
        {
          label: t`Customize Form`,
          name: 'customize-form',
          // route: `/customize-form`,
          route: `/list/CustomForm/${t`Customize Form`}`,
          hidden: () =>
            !fyo.singles.AccountingSettings?.enableFormCustomization,
        },
        {
          label: t`Settings`,
          name: 'settings',
          route: '/settings',
        },
        {
          label: t`إدارة المستخدمين`,
          name: 'user-management',
          route: '/user-management',
          hidden: () => {
            const currentUser = localStorage.getItem('currentUser');
            if (!currentUser) return true;

            // Only show for admin users
            try {
              // استخدام طريقة أخرى للتحقق من دور المستخدم
              const userRole = localStorage.getItem('userRole');
              console.log('User role from localStorage:', userRole);

              // إذا كان المستخدم هو admin، فاعرض رابط إدارة المستخدمين بغض النظر عن الدور
              if (currentUser === 'admin') {
                return false;
              }

              return userRole !== 'Admin';
            } catch (error) {
              console.error('Error checking user role:', error);
              return true;
            }
          },
        },
        {
          label: t`معلومات الترخيص`,
          name: 'license-info',
          route: '/license-info',
          hidden: () => {
            const currentUser = localStorage.getItem('currentUser');
            if (!currentUser) return true;

            // Only show for admin users
            try {
              const userRole = localStorage.getItem('userRole');

              // إذا كان المستخدم هو admin، فاعرض رابط معلومات الترخيص
              if (currentUser === 'admin') {
                return false;
              }

              return userRole !== 'Admin';
            } catch (error) {
              console.error('Error checking user role:', error);
              return true;
            }
          },
        },
      ] as SidebarItem[],
    },
  ].flat();
}
