import AccountIn from './account-in.vue';
import Address from './address.vue';
import Assets from './assets.vue';
import Calendar from './calendar.vue';
import Circle from './circle.vue';
import DownSmall from './down-small.vue';
import Down from './down.vue';
import Expenses from './expenses.vue';
import Income from './income.vue';
import Items from './items.vue';
import Liabilities from './liabilities.vue';
import Mail from './mail.vue';
import Normal from './normal.vue';
import Opened from './opened.vue';
import Phone from './phone.vue';
import Plus from './plus.vue';
import Search from './search.vue';

// prettier-ignore
export default {
  'account-in': AccountIn,
  'address': Address,
  'assets': Assets,
  'calendar': Calendar,
  'circle': Circle,
  'down-small': DownSmall,
  'down': Down,
  'expenses': Expenses,
  'income': Income,
  'items': Items,
  'liabilities': Liabilities,
  'mail': Mail,
  'normal': Normal,
  'opened': Opened,
  'phone': Phone,
  'plus': Plus,
  'search': Search,
}
