productName: newsmart
appId: io.frappe.books
asarUnpack: '**/*.node'
extraResources:
  [
    { from: 'log_creds.txt', to: '../creds/log_creds.txt' },
    { from: 'translations', to: '../translations' },
    { from: 'templates', to: '../templates' },
  ]
mac:
  type: distribution
  category: public.app-category.finance
  icon: build/icon.icns
  # notarize:
  #   appBundleId: io.frappe.books
  hardenedRuntime: true
  gatekeeperAssess: false
  darkModeSupport: false
  entitlements: build/entitlements.mac.plist
  entitlementsInherit: build/entitlements.mac.plist
  publish:
    - github
win:
  publisherName: Frappe Technologies Pvt. Ltd.
  signDlls: true
  icon: build/newsmart.ico
  publish:
    - github
  target:
    - portable
    - nsis
nsis:
  oneClick: false
  perMachine: false
  allowToChangeInstallationDirectory: true
  installerIcon: build/newsmart.ico
  uninstallerIcon: build/uninstallericon.ico
  publish:
    - github
linux:
  icon: build/icons
  category: Finance
  publish:
    - github
  target:
    - deb
    - AppImage
    - rpm