# تعليمات إضافية - دليل المطور الشامل للنظام المحاسبي

## 📋 فهرس المحتويات
1. هيكل المشروع
2. إضافة مديول جديد
3. إضافة صفحة جديدة
4. إظهار المديولات في الشريط الجانبي
5. إنشاء جداول قاعدة البيانات
6. الأوامر المستخدمة
7. حل الأخطاء الشائعة
8. إظهار المديولات المخفية
9. أمثلة عملية

---

## 🏗️ 1. هيكل المشروع

### الملفات الأساسية:
```
books/
├── src/
│   ├── components/          # المكونات المشتركة
│   ├── pages/              # الصفحات الرئيسية
│   ├── utils/              # الأدوات المساعدة
│   │   ├── sidebarConfig.ts # إعدادات الشريط الجانبي
│   │   └── theme.ts        # إعدادات الثيم
│   ├── styles/             # ملفات التصميم
│   ├── router.ts           # إعدادات التوجيه
│   └── App.vue            # التطبيق الرئيسي
├── models/                 # نماذج قاعدة البيانات
├── schemas/               # مخططات قاعدة البيانات
├── package.json           # إعدادات المشروع
└── tailwind.config.js     # إعدادات Tailwind CSS
```

### الملفات المهمة:
- `src/utils/sidebarConfig.ts` - تحكم في القائمة الجانبية
- `src/router.ts` - تحكم في المسارات
- `models/` - نماذج البيانات
- `schemas/` - مخططات قاعدة البيانات

---

## 🆕 2. إضافة مديول جديد

### الخطوة 1: إنشاء مجلد المديول
```bash
mkdir src/pages/NewModule
```

### الخطوة 2: إنشاء الصفحة الرئيسية
```vue
<!-- src/pages/NewModule/NewModule.vue -->
<template>
  <div class="new-module-page">
    <PageHeader :title="t`المديول الجديد`" />
    
    <div class="p-6">
      <div class="content-card p-6">
        <h2 class="theme-text-primary text-xl font-semibold mb-4">
          {{ t`مرحباً بك في المديول الجديد` }}
        </h2>
        <p class="theme-text-secondary">
          {{ t`هذا مديول جديد تم إضافته للنظام` }}
        </p>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import PageHeader from 'src/components/PageHeader.vue';

export default defineComponent({
  name: 'NewModule',
  components: {
    PageHeader,
  },
});
</script>
```

### الخطوة 3: إضافة المسار في router.ts
```typescript
// في src/router.ts
import NewModule from 'src/pages/NewModule/NewModule.vue';

// إضافة في routes array:
{
  path: '/new-module',
  name: 'New Module',
  component: NewModule,
  meta: { requiresAuth: true }
},
```

### الخطوة 4: إضافة في الشريط الجانبي
```typescript
// في src/utils/sidebarConfig.ts
{
  label: t`المديول الجديد`,
  name: 'new-module',
  route: '/new-module',
  icon: 'grid',
  hidden: () => false, // دائماً مرئي
},
```

---

## 📄 3. إضافة صفحة جديدة

### صفحة بسيطة:
```vue
<!-- src/pages/NewPage.vue -->
<template>
  <div class="new-page">
    <PageHeader :title="t`صفحة جديدة`" />
    
    <div class="p-6 space-y-6">
      <!-- محتوى الصفحة -->
      <div class="dashboard-card">
        <h3 class="theme-text-primary text-lg font-semibold mb-3">
          {{ t`عنوان القسم` }}
        </h3>
        <p class="theme-text-secondary">
          {{ t`محتوى الصفحة هنا` }}
        </p>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import PageHeader from 'src/components/PageHeader.vue';

export default defineComponent({
  name: 'NewPage',
  components: {
    PageHeader,
  },
  data() {
    return {
      // بيانات الصفحة
    };
  },
  methods: {
    // دوال الصفحة
  },
});
</script>
```

### إضافة المسار:
```typescript
// في src/router.ts
import NewPage from 'src/pages/NewPage.vue';

{
  path: '/new-page',
  name: 'New Page',
  component: NewPage,
  meta: { requiresAuth: true }
},
```

---

## 🔧 4. إظهار المديولات في الشريط الجانبي

### إضافة مديول رئيسي:
```typescript
// في src/utils/sidebarConfig.ts
{
  label: t`اسم المديول`,
  name: 'module-name',
  route: '/module-route',
  icon: 'icon-name', // من Feather Icons
  hidden: () => {
    // شروط الإظهار/الإخفاء
    const currentUser = localStorage.getItem('currentUser');
    return !currentUser; // مخفي إذا لم يكن هناك مستخدم
  },
},
```

### إضافة مديول بصفحات فرعية:
```typescript
{
  label: t`المديول الرئيسي`,
  name: 'main-module',
  route: '/main-module',
  icon: 'folder',
  items: [
    {
      label: t`الصفحة الفرعية 1`,
      name: 'sub-page-1',
      route: '/main-module/sub-page-1',
    },
    {
      label: t`الصفحة الفرعية 2`,
      name: 'sub-page-2',
      route: '/main-module/sub-page-2',
    },
  ],
  hidden: () => false,
},
```

### شروط الإظهار المختلفة:
```typescript
// إظهار للمدير فقط
hidden: () => {
  const userRole = localStorage.getItem('userRole');
  return userRole !== 'Admin';
},

// إظهار لمستخدمين محددين
hidden: () => {
  const currentUser = localStorage.getItem('currentUser');
  const allowedUsers = ['admin', 'manager'];
  return !allowedUsers.includes(currentUser);
},

// إظهار حسب الصلاحيات
hidden: () => {
  const permissions = JSON.parse(localStorage.getItem('userPermissions') || '[]');
  return !permissions.includes('module_access');
},
```

---

## 🗄️ 5. إنشاء جداول قاعدة البيانات

### الخطوة 1: إنشاء Schema
```javascript
// في schemas/app/NewTable.json
{
  "name": "NewTable",
  "label": "الجدول الجديد",
  "naming": "autoincrement",
  "fields": [
    {
      "fieldname": "name",
      "label": "الاسم",
      "fieldtype": "Data",
      "required": true
    },
    {
      "fieldname": "description",
      "label": "الوصف",
      "fieldtype": "Text"
    },
    {
      "fieldname": "amount",
      "label": "المبلغ",
      "fieldtype": "Currency"
    },
    {
      "fieldname": "date",
      "label": "التاريخ",
      "fieldtype": "Date"
    },
    {
      "fieldname": "isActive",
      "label": "نشط",
      "fieldtype": "Check",
      "default": true
    }
  ]
}
```

### الخطوة 2: إنشاء Model
```typescript
// في models/app/NewTable.ts
import { Doc } from 'fyo/model/doc';
import { FiltersMap, FormulaMap } from 'fyo/model/types';

export class NewTable extends Doc {
  name?: string;
  description?: string;
  amount?: number;
  date?: Date;
  isActive?: boolean;

  // دوال مخصصة
  async beforeInsert() {
    // منطق قبل الإدراج
  }

  async afterInsert() {
    // منطق بعد الإدراج
  }

  // فلاتر
  static filters: FiltersMap = {
    // فلاتر مخصصة
  };

  // صيغ محسوبة
  static formulas: FormulaMap = {
    // صيغ مخصصة
  };
}
```

### الخطوة 3: تسجيل النموذج
```typescript
// في models/index.ts
import { NewTable } from './app/NewTable';

export const models = {
  // النماذج الموجودة...
  NewTable,
};
```

### الخطوة 4: إنشاء صفحة إدارة البيانات
```vue
<!-- src/pages/NewTable/NewTableList.vue -->
<template>
  <div class="new-table-list">
    <PageHeader :title="t`إدارة الجدول الجديد`">
      <template #actions>
        <Button @click="createNew">{{ t`إضافة جديد` }}</Button>
      </template>
    </PageHeader>
    
    <div class="p-6">
      <!-- جدول البيانات -->
      <div class="table-container">
        <table class="w-full">
          <thead class="table-header">
            <tr>
              <th class="px-4 py-3 text-right">{{ t`الاسم` }}</th>
              <th class="px-4 py-3 text-right">{{ t`الوصف` }}</th>
              <th class="px-4 py-3 text-right">{{ t`المبلغ` }}</th>
              <th class="px-4 py-3 text-right">{{ t`التاريخ` }}</th>
              <th class="px-4 py-3 text-right">{{ t`الإجراءات` }}</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="item in items" :key="item.name" class="table-row border-b">
              <td class="px-4 py-3">{{ item.name }}</td>
              <td class="px-4 py-3">{{ item.description }}</td>
              <td class="px-4 py-3">{{ item.amount }}</td>
              <td class="px-4 py-3">{{ item.date }}</td>
              <td class="px-4 py-3">
                <button @click="editItem(item)" class="btn-primary px-3 py-1 text-sm rounded">
                  {{ t`تعديل` }}
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { fyo } from 'src/initFyo';

export default defineComponent({
  name: 'NewTableList',
  data() {
    return {
      items: [],
    };
  },
  async mounted() {
    await this.loadData();
  },
  methods: {
    async loadData() {
      try {
        this.items = await fyo.db.getAll('NewTable');
      } catch (error) {
        console.error('Error loading data:', error);
      }
    },
    
    async createNew() {
      const doc = fyo.doc.getNewDoc('NewTable');
      // فتح نموذج التحرير
    },
    
    async editItem(item) {
      const doc = await fyo.doc.getDoc('NewTable', item.name);
      // فتح نموذج التحرير
    },
  },
});
</script>
```

---

## ⚡ 6. الأوامر المستخدمة

### أوامر التطوير:
```bash
# تشغيل النظام في وضع التطوير
npm run dev

# بناء النظام للإنتاج
npm run build

# تشغيل الاختبارات
npm test

# فحص الكود
npm run lint

# إصلاح مشاكل الكود
npm run lint:fix
```

### أوامر قاعدة البيانات:
```bash
# إنشاء قاعدة بيانات جديدة
npm run db:create

# تحديث مخططات قاعدة البيانات
npm run db:migrate

# إعادة تعيين قاعدة البيانات
npm run db:reset
```

### أوامر Git:
```bash
# إضافة التغييرات
git add .

# حفظ التغييرات
git commit -m "وصف التغيير"

# رفع التغييرات
git push origin main

# سحب التحديثات
git pull origin main
```

---

## 🔧 7. حل الأخطاء الشائعة

### خطأ: Module not found
```bash
# المشكلة: لا يمكن العثور على المديول
# الحل:
1. تأكد من مسار الملف
2. تأكد من تصدير المكون بشكل صحيح
3. أعد تشغيل الخادم: npm run dev
```

### خطأ: Cannot read property of undefined
```javascript
// المشكلة: محاولة الوصول لخاصية غير موجودة
// الحل:
// بدلاً من:
const value = data.property;

// استخدم:
const value = data?.property || 'قيمة افتراضية';
```

### خطأ: Router not found
```typescript
// المشكلة: المسار غير مسجل
// الحل: تأكد من إضافة المسار في router.ts
{
  path: '/your-path',
  name: 'Your Page',
  component: YourComponent,
}
```

### خطأ: Database schema not found
```bash
# المشكلة: مخطط قاعدة البيانات غير موجود
# الحل:
1. تأكد من وجود ملف Schema في schemas/app/
2. أعد تشغيل النظام
3. تحقق من صحة JSON في ملف Schema
```

### خطأ: CSS classes not working
```bash
# المشكلة: كلاسات CSS لا تعمل
# الحل:
1. تأكد من استيراد ملفات CSS
2. تحقق من Tailwind config
3. امسح cache: rm -rf node_modules/.cache
```

---

## 👁️ 8. إظهار المديولات المخفية

### إظهار معلومات الترخيص:
```typescript
// في src/utils/sidebarConfig.ts
{
  label: t`معلومات الترخيص`,
  name: 'license-info',
  route: '/license-info',
  hidden: () => {
    // غير الشرط من true إلى false
    return false; // سيظهر المديول
  },
},
```

### إظهار مولد التراخيص:
```typescript
{
  label: t`مولد التراخيص`,
  name: 'license-generator',
  route: '/license-generator',
  hidden: () => {
    // إظهار للمدير فقط
    const currentUser = localStorage.getItem('currentUser');
    return currentUser !== 'admin';
  },
},
```

### إظهار مديول حسب الصلاحيات:
```typescript
{
  label: t`المديول المخفي`,
  name: 'hidden-module',
  route: '/hidden-module',
  hidden: () => {
    // شروط مخصصة للإظهار
    const userRole = localStorage.getItem('userRole');
    const currentUser = localStorage.getItem('currentUser');
    
    // إظهار للمدير أو مستخدمين محددين
    return !(userRole === 'Admin' || currentUser === 'special_user');
  },
},
```

---

## 📝 9. أمثلة عملية

### مثال 1: إضافة مديول إدارة العملاء
```bash
# 1. إنشاء المجلد
mkdir src/pages/Customers

# 2. إنشاء الملفات
touch src/pages/Customers/CustomerList.vue
touch src/pages/Customers/CustomerForm.vue
```

```vue
<!-- CustomerList.vue -->
<template>
  <div class="customers-page">
    <PageHeader :title="t`إدارة العملاء`">
      <template #actions>
        <Button @click="addCustomer">{{ t`إضافة عميل` }}</Button>
      </template>
    </PageHeader>
    <!-- باقي المحتوى -->
  </div>
</template>
```

```typescript
// إضافة في router.ts
{
  path: '/customers',
  name: 'Customers',
  component: () => import('src/pages/Customers/CustomerList.vue'),
  meta: { requiresAuth: true }
},
```

```typescript
// إضافة في sidebarConfig.ts
{
  label: t`العملاء`,
  name: 'customers',
  route: '/customers',
  icon: 'users',
  hidden: () => false,
},
```

### مثال 2: إنشاء جدول المنتجات
```json
// schemas/app/Product.json
{
  "name": "Product",
  "label": "المنتج",
  "fields": [
    {
      "fieldname": "name",
      "label": "اسم المنتج",
      "fieldtype": "Data",
      "required": true
    },
    {
      "fieldname": "price",
      "label": "السعر",
      "fieldtype": "Currency",
      "required": true
    },
    {
      "fieldname": "category",
      "label": "الفئة",
      "fieldtype": "Link",
      "target": "Category"
    }
  ]
}
```

---

## 🚀 10. نصائح للتطوير

### أفضل الممارسات:
1. **استخدم TypeScript** لتجنب الأخطاء
2. **اتبع تسمية موحدة** للملفات والمتغيرات
3. **اكتب تعليقات** للكود المعقد
4. **اختبر التغييرات** قبل الحفظ
5. **استخدم Git** لحفظ التغييرات

### تحسين الأداء:
1. **استخدم lazy loading** للصفحات الكبيرة
2. **قلل من استيراد المكتبات** غير الضرورية
3. **استخدم computed properties** بدلاً من methods
4. **احذف console.log** في الإنتاج

### الأمان:
1. **تحقق من صلاحيات المستخدم** في كل صفحة
2. **لا تحفظ كلمات المرور** في localStorage
3. **استخدم HTTPS** في الإنتاج
4. **تحقق من البيانات** قبل الحفظ

---

## 📞 الدعم والمساعدة

### عند مواجهة مشاكل:
1. **تحقق من وحدة التحكم** للأخطاء
2. **راجع ملفات السجل** للتفاصيل
3. **ابحث في التوثيق** عن حلول
4. **اسأل المجتمع** في المنتديات

### موارد مفيدة:
- **Vue.js Documentation**: https://vuejs.org/
- **TypeScript Handbook**: https://www.typescriptlang.org/
- **Tailwind CSS**: https://tailwindcss.com/
- **Frappe Framework**: https://frappeframework.com/

---

---

## 🔍 11. أمثلة متقدمة

### إنشاء مديول تقارير مخصص:
```vue
<!-- src/pages/Reports/CustomReports.vue -->
<template>
  <div class="custom-reports">
    <PageHeader :title="t`التقارير المخصصة`" />

    <div class="p-6 space-y-6">
      <!-- فلاتر التقرير -->
      <div class="content-card p-6">
        <h3 class="theme-text-primary text-lg font-semibold mb-4">{{ t`فلاتر التقرير` }}</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label class="theme-text-primary font-medium">{{ t`من تاريخ` }}</label>
            <input v-model="filters.fromDate" type="date" class="form-input w-full mt-1">
          </div>
          <div>
            <label class="theme-text-primary font-medium">{{ t`إلى تاريخ` }}</label>
            <input v-model="filters.toDate" type="date" class="form-input w-full mt-1">
          </div>
          <div>
            <label class="theme-text-primary font-medium">{{ t`نوع التقرير` }}</label>
            <select v-model="filters.reportType" class="form-input w-full mt-1">
              <option value="sales">{{ t`المبيعات` }}</option>
              <option value="purchases">{{ t`المشتريات` }}</option>
              <option value="inventory">{{ t`المخزون` }}</option>
            </select>
          </div>
        </div>
        <div class="mt-4">
          <button @click="generateReport" class="btn-primary px-6 py-2 rounded-lg">
            {{ t`إنشاء التقرير` }}
          </button>
        </div>
      </div>

      <!-- نتائج التقرير -->
      <div v-if="reportData.length > 0" class="content-card p-6">
        <h3 class="theme-text-primary text-lg font-semibold mb-4">{{ t`نتائج التقرير` }}</h3>
        <div class="table-container">
          <table class="w-full">
            <thead class="table-header">
              <tr>
                <th v-for="column in reportColumns" :key="column.key" class="px-4 py-3 text-right">
                  {{ column.label }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="row in reportData" :key="row.id" class="table-row border-b">
                <td v-for="column in reportColumns" :key="column.key" class="px-4 py-3">
                  {{ formatValue(row[column.key], column.type) }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { fyo } from 'src/initFyo';

export default defineComponent({
  name: 'CustomReports',
  data() {
    return {
      filters: {
        fromDate: '',
        toDate: '',
        reportType: 'sales'
      },
      reportData: [],
      reportColumns: []
    };
  },
  methods: {
    async generateReport() {
      try {
        // منطق إنشاء التقرير حسب النوع
        switch (this.filters.reportType) {
          case 'sales':
            await this.generateSalesReport();
            break;
          case 'purchases':
            await this.generatePurchasesReport();
            break;
          case 'inventory':
            await this.generateInventoryReport();
            break;
        }
      } catch (error) {
        console.error('Error generating report:', error);
      }
    },

    async generateSalesReport() {
      this.reportColumns = [
        { key: 'date', label: 'التاريخ', type: 'date' },
        { key: 'customer', label: 'العميل', type: 'text' },
        { key: 'amount', label: 'المبلغ', type: 'currency' }
      ];

      // استعلام قاعدة البيانات
      this.reportData = await fyo.db.getAll('SalesInvoice', {
        filters: {
          date: ['between', [this.filters.fromDate, this.filters.toDate]]
        }
      });
    },

    formatValue(value: any, type: string) {
      switch (type) {
        case 'currency':
          return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR'
          }).format(value);
        case 'date':
          return new Date(value).toLocaleDateString('ar-SA');
        default:
          return value;
      }
    }
  }
});
</script>
```

### إنشاء مكون قابل لإعادة الاستخدام:
```vue
<!-- src/components/DataTable.vue -->
<template>
  <div class="data-table">
    <div class="table-container">
      <table class="w-full">
        <thead class="table-header">
          <tr>
            <th v-for="column in columns" :key="column.key"
                class="px-4 py-3 text-right cursor-pointer"
                @click="sortBy(column.key)">
              {{ column.label }}
              <span v-if="sortColumn === column.key">
                {{ sortDirection === 'asc' ? '↑' : '↓' }}
              </span>
            </th>
            <th v-if="actions.length > 0" class="px-4 py-3 text-right">{{ t`الإجراءات` }}</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="row in sortedData" :key="row.id || row.name" class="table-row border-b">
            <td v-for="column in columns" :key="column.key" class="px-4 py-3">
              <slot :name="`cell-${column.key}`" :row="row" :value="row[column.key]">
                {{ formatCellValue(row[column.key], column.type) }}
              </slot>
            </td>
            <td v-if="actions.length > 0" class="px-4 py-3">
              <div class="flex space-x-2 space-x-reverse">
                <button v-for="action in actions" :key="action.name"
                        @click="$emit('action', action.name, row)"
                        :class="action.class || 'btn-primary px-3 py-1 text-sm rounded'">
                  {{ action.label }}
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <div v-if="pagination" class="flex justify-between items-center mt-4">
      <div class="theme-text-secondary">
        {{ t`عرض` }} {{ startIndex }} - {{ endIndex }} {{ t`من` }} {{ totalItems }}
      </div>
      <div class="flex space-x-2 space-x-reverse">
        <button @click="previousPage" :disabled="currentPage === 1"
                class="px-3 py-1 border rounded disabled:opacity-50">
          {{ t`السابق` }}
        </button>
        <button @click="nextPage" :disabled="currentPage === totalPages"
                class="px-3 py-1 border rounded disabled:opacity-50">
          {{ t`التالي` }}
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue';

export default defineComponent({
  name: 'DataTable',
  props: {
    data: { type: Array, required: true },
    columns: { type: Array, required: true },
    actions: { type: Array, default: () => [] },
    pagination: { type: Boolean, default: false },
    itemsPerPage: { type: Number, default: 10 }
  },
  emits: ['action'],
  data() {
    return {
      sortColumn: '',
      sortDirection: 'asc',
      currentPage: 1
    };
  },
  computed: {
    sortedData() {
      let data = [...this.data];

      if (this.sortColumn) {
        data.sort((a, b) => {
          const aVal = a[this.sortColumn];
          const bVal = b[this.sortColumn];

          if (this.sortDirection === 'asc') {
            return aVal > bVal ? 1 : -1;
          } else {
            return aVal < bVal ? 1 : -1;
          }
        });
      }

      if (this.pagination) {
        const start = (this.currentPage - 1) * this.itemsPerPage;
        const end = start + this.itemsPerPage;
        return data.slice(start, end);
      }

      return data;
    },

    totalItems() {
      return this.data.length;
    },

    totalPages() {
      return Math.ceil(this.totalItems / this.itemsPerPage);
    },

    startIndex() {
      return (this.currentPage - 1) * this.itemsPerPage + 1;
    },

    endIndex() {
      return Math.min(this.currentPage * this.itemsPerPage, this.totalItems);
    }
  },
  methods: {
    sortBy(column: string) {
      if (this.sortColumn === column) {
        this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
      } else {
        this.sortColumn = column;
        this.sortDirection = 'asc';
      }
    },

    formatCellValue(value: any, type: string) {
      switch (type) {
        case 'currency':
          return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR'
          }).format(value);
        case 'date':
          return new Date(value).toLocaleDateString('ar-SA');
        case 'boolean':
          return value ? 'نعم' : 'لا';
        default:
          return value;
      }
    },

    previousPage() {
      if (this.currentPage > 1) {
        this.currentPage--;
      }
    },

    nextPage() {
      if (this.currentPage < this.totalPages) {
        this.currentPage++;
      }
    }
  }
});
</script>
```

---

## 🛠️ 12. أدوات التطوير المتقدمة

### إعداد ESLint للجودة:
```json
// .eslintrc.js
module.exports = {
  extends: [
    '@vue/typescript/recommended',
    'prettier'
  ],
  rules: {
    'no-console': 'warn',
    'no-debugger': 'error',
    '@typescript-eslint/no-unused-vars': 'error',
    'vue/no-unused-components': 'error'
  }
};
```

### إعداد Prettier للتنسيق:
```json
// .prettierrc
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2
}
```

### إعداد Husky للـ Git Hooks:
```json
// package.json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged",
      "pre-push": "npm test"
    }
  },
  "lint-staged": {
    "*.{js,ts,vue}": ["eslint --fix", "prettier --write"]
  }
}
```

---

## 📊 13. مراقبة الأداء والتحليل

### إضافة مراقبة الأداء:
```typescript
// src/utils/performance.ts
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, number> = new Map();

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  startTimer(name: string): void {
    this.metrics.set(name, performance.now());
  }

  endTimer(name: string): number {
    const startTime = this.metrics.get(name);
    if (!startTime) return 0;

    const duration = performance.now() - startTime;
    console.log(`${name} took ${duration.toFixed(2)}ms`);
    return duration;
  }

  measureFunction<T>(name: string, fn: () => T): T {
    this.startTimer(name);
    const result = fn();
    this.endTimer(name);
    return result;
  }
}

// الاستخدام:
const monitor = PerformanceMonitor.getInstance();
monitor.startTimer('data-load');
// ... تحميل البيانات
monitor.endTimer('data-load');
```

### إضافة تسجيل الأخطاء:
```typescript
// src/utils/errorLogger.ts
export class ErrorLogger {
  static logError(error: Error, context?: string): void {
    const errorInfo = {
      message: error.message,
      stack: error.stack,
      context: context || 'Unknown',
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    // إرسال للخادم أو حفظ محلياً
    console.error('Error logged:', errorInfo);

    // يمكن إرسال للخادم
    // this.sendToServer(errorInfo);
  }

  static async sendToServer(errorInfo: any): Promise<void> {
    try {
      await fetch('/api/errors', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(errorInfo)
      });
    } catch (e) {
      console.error('Failed to send error to server:', e);
    }
  }
}
```

---

## 🔐 14. الأمان والصلاحيات المتقدمة

### إنشاء نظام صلاحيات متقدم:
```typescript
// src/utils/permissions.ts
export interface Permission {
  module: string;
  action: 'read' | 'write' | 'delete' | 'admin';
  resource?: string;
}

export class PermissionManager {
  private static userPermissions: Permission[] = [];

  static setUserPermissions(permissions: Permission[]): void {
    this.userPermissions = permissions;
  }

  static hasPermission(module: string, action: string, resource?: string): boolean {
    return this.userPermissions.some(permission =>
      permission.module === module &&
      permission.action === action &&
      (!resource || permission.resource === resource)
    );
  }

  static canAccess(route: string): boolean {
    const routePermissions = {
      '/sales': { module: 'sales', action: 'read' },
      '/purchases': { module: 'purchases', action: 'read' },
      '/reports': { module: 'reports', action: 'read' },
      '/users': { module: 'users', action: 'admin' }
    };

    const required = routePermissions[route];
    if (!required) return true;

    return this.hasPermission(required.module, required.action);
  }
}

// استخدام في router guard:
router.beforeEach((to, from, next) => {
  if (PermissionManager.canAccess(to.path)) {
    next();
  } else {
    next('/unauthorized');
  }
});
```

---

## 📱 15. التوافق مع الأجهزة المحمولة

### إضافة تصميم متجاوب:
```vue
<!-- src/components/ResponsiveLayout.vue -->
<template>
  <div class="responsive-layout">
    <!-- Desktop Sidebar -->
    <div v-if="!isMobile" class="sidebar-container">
      <Sidebar />
    </div>

    <!-- Mobile Menu -->
    <div v-if="isMobile" class="mobile-menu">
      <button @click="toggleMobileMenu" class="menu-toggle">
        <feather-icon name="menu" />
      </button>

      <div v-if="showMobileMenu" class="mobile-sidebar">
        <Sidebar @item-click="closeMobileMenu" />
      </div>
    </div>

    <!-- Main Content -->
    <div class="main-content" :class="{ 'mobile': isMobile }">
      <slot />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'ResponsiveLayout',
  data() {
    return {
      isMobile: false,
      showMobileMenu: false
    };
  },
  mounted() {
    this.checkScreenSize();
    window.addEventListener('resize', this.checkScreenSize);
  },
  unmounted() {
    window.removeEventListener('resize', this.checkScreenSize);
  },
  methods: {
    checkScreenSize() {
      this.isMobile = window.innerWidth < 768;
    },

    toggleMobileMenu() {
      this.showMobileMenu = !this.showMobileMenu;
    },

    closeMobileMenu() {
      this.showMobileMenu = false;
    }
  }
});
</script>

<style scoped>
.mobile-menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: var(--content-bg);
  padding: 1rem;
  border-bottom: 1px solid var(--content-border);
}

.mobile-sidebar {
  position: fixed;
  top: 60px;
  left: 0;
  bottom: 0;
  width: 280px;
  background: var(--sidebar-bg);
  z-index: 999;
  transform: translateX(0);
  transition: transform 0.3s ease;
}

.main-content.mobile {
  padding-top: 60px;
}

@media (max-width: 768px) {
  .sidebar-container {
    display: none;
  }
}
</style>
```

---

**تم إنشاء هذا الدليل الشامل لمساعدة المطورين في فهم وتطوير النظام المحاسبي بشكل احترافي** 🚀

**آخر تحديث:** 2024-12-16
**الإصدار:** 2.0.0
