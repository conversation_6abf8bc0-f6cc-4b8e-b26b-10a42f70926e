<template>
  <div
    class="
      flex
      items-center
      justify-center
      h-full
      bg-gray-50
      dark:bg-gray-900
    "
  >
    <div
      class="
        w-96
        p-8
        bg-white
        dark:bg-gray-800
        rounded-lg
        shadow-lg
        border
        dark:border-gray-700
      "
    >
      <div class="text-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800 dark:text-white">
          {{ t`إعادة تعيين كلمة المرور` }}
        </h1>
        <p class="text-gray-600 dark:text-gray-400 mt-2">
          {{ t`الرجاء إدخال كلمة المرور الجديدة` }}
        </p>
      </div>

      <div v-if="errorMessage" class="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
        {{ errorMessage }}
      </div>

      <div v-if="successMessage" class="mb-4 p-3 bg-green-100 text-green-700 rounded-md">
        {{ successMessage }}
      </div>

      <form @submit.prevent="resetPassword">
        <div class="mb-4">
          <label
            for="username"
            class="block text-gray-700 dark:text-gray-300 mb-2"
          >
            {{ t`اسم المستخدم` }}
          </label>
          <input
            id="username"
            v-model="username"
            type="text"
            class="
              w-full
              px-3
              py-2
              border
              rounded-md
              focus:outline-none
              focus:ring-2
              focus:ring-blue-500
              dark:bg-gray-700
              dark:border-gray-600
              dark:text-white
            "
            required
          />
        </div>

        <div class="mb-4">
          <label
            for="password"
            class="block text-gray-700 dark:text-gray-300 mb-2"
          >
            {{ t`كلمة المرور الجديدة` }}
          </label>
          <input
            id="password"
            v-model="password"
            type="password"
            class="
              w-full
              px-3
              py-2
              border
              rounded-md
              focus:outline-none
              focus:ring-2
              focus:ring-blue-500
              dark:bg-gray-700
              dark:border-gray-600
              dark:text-white
            "
            required
          />
        </div>

        <div class="mb-6">
          <label
            for="confirmPassword"
            class="block text-gray-700 dark:text-gray-300 mb-2"
          >
            {{ t`تأكيد كلمة المرور` }}
          </label>
          <input
            id="confirmPassword"
            v-model="confirmPassword"
            type="password"
            class="
              w-full
              px-3
              py-2
              border
              rounded-md
              focus:outline-none
              focus:ring-2
              focus:ring-blue-500
              dark:bg-gray-700
              dark:border-gray-600
              dark:text-white
            "
            required
          />
        </div>

        <button
          type="submit"
          class="
            w-full
            py-2
            px-4
            bg-blue-600
            hover:bg-blue-700
            text-white
            rounded-md
            focus:outline-none
            focus:ring-2
            focus:ring-blue-500
            focus:ring-offset-2
          "
          :disabled="isLoading"
        >
          <span v-if="isLoading">{{ t`جاري إعادة تعيين كلمة المرور...` }}</span>
          <span v-else>{{ t`إعادة تعيين كلمة المرور` }}</span>
        </button>
      </form>

      <div class="mt-4 text-center">
        <a
          href="/login"
          class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
        >
          {{ t`العودة إلى صفحة تسجيل الدخول` }}
        </a>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { fyo } from 'src/initFyo';
import { showToast } from 'src/utils/interactive';

export default defineComponent({
  name: 'ResetPassword',
  data() {
    return {
      username: '',
      password: '',
      confirmPassword: '',
      errorMessage: '',
      successMessage: '',
      isLoading: false,
    };
  },
  methods: {
    async resetPassword() {
      this.isLoading = true;
      this.errorMessage = '';
      this.successMessage = '';

      try {
        // التحقق من تطابق كلمتي المرور
        if (this.password !== this.confirmPassword) {
          this.errorMessage = this.t`كلمتا المرور غير متطابقتين`;
          this.isLoading = false;
          return;
        }

        // التحقق من وجود المستخدم
        const exists = await fyo.db.exists('User', this.username);
        if (!exists) {
          this.errorMessage = this.t`اسم المستخدم غير موجود`;
          this.isLoading = false;
          return;
        }

        // الحصول على وثيقة المستخدم
        const user = await fyo.doc.getDoc('User', this.username);

        // تعيين كلمة المرور الجديدة
        // استخدام دالة hashPassword في كائن المستخدم
        const hashedPassword = user.hashPassword(this.password);
        console.log('New hashed password:', hashedPassword);

        await user.set('password', hashedPassword);
        await user.sync();

        this.successMessage = this.t`تم إعادة تعيين كلمة المرور بنجاح`;

        // مسح النموذج
        this.username = '';
        this.password = '';
        this.confirmPassword = '';

        showToast({
          message: this.t`تم إعادة تعيين كلمة المرور بنجاح`,
          type: 'success',
        });
      } catch (error) {
        console.error('Error resetting password:', error);
        this.errorMessage = this.t`حدث خطأ أثناء إعادة تعيين كلمة المرور: ${error.message || error}`;
      } finally {
        this.isLoading = false;
      }
    },
  },
});
</script>
