# تعليمات المطور لنظام إدارة المستخدمين والصلاحيات

## الملفات التي تم تعديلها

### ملفات نظام إدارة المستخدمين والصلاحيات
1. `src/pages/Login.vue` - صفحة تسجيل الدخول
2. `src/pages/UserManagement.vue` - صفحة إدارة المستخدمين
3. `models/baseModels/User/User.ts` - نموذج المستخدم
4. `schemas/User.json` - مخطط المستخدم
5. `schemas/UserPermission.json` - مخطط صلاحيات المستخدم (تم إنشاؤه)
6. `src/utils/sidebarConfig.ts` - تكوين القائمة الجانبية
7. `src/utils/types.ts` - أنواع البيانات
8. `src/components/Sidebar.vue` - مكون القائمة الجانبية
9. `src/pages/FixUserRole.vue` - صفحة إصلاح دور المستخدم (تم إنشاؤها)

### ملفات نظام الترخيص
10. `schemas/License.json` - مخطط الترخيص (تم إنشاؤه)
11. `models/baseModels/License/License.ts` - نموذج الترخيص (تم إنشاؤه)
12. `src/utils/licenseManager.ts` - خدمة إدارة الترخيص (تم إنشاؤها)
13. `src/pages/LicenseActivation.vue` - صفحة تفعيل الترخيص (تم إنشاؤها)
14. `src/pages/LicenseInfo.vue` - صفحة معلومات الترخيص (تم إنشاؤها)
15. `src/router.ts` - مسارات التطبيق (تم تحديثه)

## التعديلات الرئيسية

### 1. نظام إدارة المستخدمين

#### ملف `src/pages/UserManagement.vue`
- يحتوي على واجهة إدارة المستخدمين
- يمكن إضافة مستخدم جديد أو تعديل مستخدم حالي أو حذف مستخدم
- يعرض قائمة المستخدمين مع معلومات عن حالة المستخدم وصلاحياته
- يحتوي على نموذج لإدخال بيانات المستخدم (الاسم، البريد الإلكتروني، كلمة المرور، إلخ)
- يحتوي على خيارات لتحديد دور المستخدم وحالته وصلاحياته

الدوال الرئيسية:
- `loadUsers()` - تحميل قائمة المستخدمين
- `editUser(user)` - تعديل مستخدم
- `deleteUser(user)` - حذف مستخدم
- `saveUser()` - حفظ بيانات المستخدم
- `saveUserPermissions(userName)` - حفظ صلاحيات المستخدم
- `loadUserPermissions(userName)` - تحميل صلاحيات المستخدم
- `isUserActive(user)` - التحقق من حالة نشاط المستخدم
- `hasAllPermissions(user)` - التحقق من صلاحيات المستخدم

### 2. نظام الصلاحيات

#### ملف `schemas/UserPermission.json`
- مخطط صلاحيات المستخدم
- يحتوي على حقول: الاسم، المستخدم، الوحدة، نشط

#### ملف `models/baseModels/User/User.ts`
- نموذج المستخدم
- تم إضافة حقل `hasAllPermissions` للإشارة إلى ما إذا كان المستخدم لديه جميع الصلاحيات
- تم إضافة دالة `hasPermissionFor(module)` للتحقق من صلاحيات المستخدم للوحدة المحددة

#### ملف `src/utils/sidebarConfig.ts`
- تكوين القائمة الجانبية
- تم إضافة دالة `checkUserPermission(module)` للتحقق من صلاحيات المستخدم
- تم تعديل دالة `getFilteredSidebar(sideBar)` لتصفية العناصر بناءً على صلاحيات المستخدم
- تم إضافة حقل `module` إلى كل وحدة في القائمة الجانبية

#### ملف `src/utils/types.ts`
- أنواع البيانات
- تم إضافة حقل `module` إلى نوع `SidebarRoot`

#### ملف `src/components/Sidebar.vue`
- مكون القائمة الجانبية
- تم تعديل دالة `mounted()` لاستخدام دالة `getSidebarConfig()` المتزامنة
- تم إضافة دالة `loadSidebarConfig()` لتحميل تكوين القائمة الجانبية

### 3. تخزين الصلاحيات

نظرًا لعدم وجود مخطط `UserPermission` في قاعدة البيانات، فإن الصلاحيات يتم تخزينها في `localStorage`:

- `permissions_${userName}` - صلاحيات المستخدم
- `hasAllPermissions_${userName}` - ما إذا كان المستخدم لديه جميع الصلاحيات

## كيفية إضافة وحدة (مديول) جديدة

لإضافة وحدة جديدة إلى النظام، اتبع الخطوات التالية:

1. **إضافة الوحدة إلى قائمة الوحدات المتاحة في `UserManagement.vue`**:
   ```javascript
   availableModules: [
     { value: 'sales', label: this.t`المبيعات` },
     { value: 'purchases', label: this.t`المشتريات` },
     { value: 'inventory', label: this.t`المخزون` },
     { value: 'common', label: this.t`العام` },
     { value: 'reports', label: this.t`التقارير` },
     { value: 'pos', label: this.t`نقاط البيع` },
     { value: 'setup', label: this.t`الإعدادات` },
     // أضف الوحدة الجديدة هنا
     { value: 'new_module', label: this.t`الوحدة الجديدة` },
   ],
   ```

2. **إضافة الوحدة إلى القائمة الجانبية في `sidebarConfig.ts`**:
   ```javascript
   {
     label: t`الوحدة الجديدة`,
     name: 'new-module',
     icon: 'icon-name',
     route: '/route-to-module',
     module: 'new_module', // نفس القيمة المستخدمة في availableModules
     items: [
       // العناصر الفرعية للوحدة
       {
         label: t`العنصر الأول`,
         name: 'first-item',
         route: '/route-to-first-item',
       },
       // المزيد من العناصر
     ] as SidebarItem[],
   },
   ```

3. **إضافة مسار الوحدة في `router.ts`**:
   ```javascript
   {
     path: '/route-to-module',
     name: 'New Module',
     component: NewModuleComponent,
     meta: { requiresAuth: true }
   },
   ```

4. **إنشاء مكون الوحدة**:
   - إنشاء ملف جديد في مجلد `src/pages` للوحدة الجديدة
   - تنفيذ واجهة المستخدم والمنطق الخاص بالوحدة

## كيفية إضافة صفحة جديدة

لإضافة صفحة جديدة إلى النظام، اتبع الخطوات التالية:

1. **إنشاء ملف الصفحة**:
   - إنشاء ملف جديد في مجلد `src/pages` للصفحة الجديدة
   - تنفيذ واجهة المستخدم والمنطق الخاص بالصفحة

2. **إضافة مسار الصفحة في `router.ts`**:
   ```javascript
   {
     path: '/route-to-page',
     name: 'New Page',
     component: NewPageComponent,
     meta: { requiresAuth: true } // إذا كانت الصفحة تتطلب تسجيل الدخول
   },
   ```

3. **إضافة رابط للصفحة في القائمة الجانبية (اختياري)**:
   - إضافة عنصر جديد في `sidebarConfig.ts` للصفحة
   - تحديد الوحدة التي تنتمي إليها الصفحة

## الأوامر المستخدمة

### 1. التعامل مع المستخدمين

```javascript
// إنشاء مستخدم جديد
const userData = {
  name: 'username',
  fullName: 'Full Name',
  email: '<EMAIL>',
  password: hashedPassword,
  role: 'User', // أو 'Admin'
  isActive: 1,
  hasAllPermissions: 0,
};
const user = fyo.doc.getNewDoc('User', userData);
await user.sync();

// الحصول على مستخدم
const user = await fyo.doc.getDoc('User', 'username');

// تحديث مستخدم
await user.set('fullName', 'New Full Name');
await user.set('email', '<EMAIL>');
await user.set('role', 'Admin');
await user.set('isActive', 1);
await user.set('hasAllPermissions', 1);
await user.sync();

// حذف مستخدم
await fyo.db.delete('User', 'username');
```

### 2. التعامل مع الصلاحيات

```javascript
// حفظ صلاحيات المستخدم في localStorage
const userPermissions = {
  user: 'username',
  modules: ['sales', 'inventory'],
  timestamp: new Date().toISOString()
};
localStorage.setItem(`permissions_${username}`, JSON.stringify(userPermissions));

// تحميل صلاحيات المستخدم من localStorage
const storedPermissions = localStorage.getItem(`permissions_${username}`);
const parsedPermissions = JSON.parse(storedPermissions);

// التحقق من صلاحيات المستخدم
const hasPermission = parsedPermissions.modules.includes('module_name');
```

### 3. التعامل مع القائمة الجانبية

```javascript
// تحميل تكوين القائمة الجانبية
const sideBar = getCompleteSidebar();
const filteredSidebar = await getFilteredSidebar(sideBar);

// التحقق من صلاحيات المستخدم للوحدة
const hasPermission = await checkUserPermission('module_name');
```

### 4. نظام الترخيص

#### ملف `schemas/License.json`
- مخطط الترخيص
- يحتوي على حقول: مفتاح الترخيص، اسم الشركة، البريد الإلكتروني، تاريخ الإصدار، تاريخ الانتهاء، الحد الأقصى للمستخدمين، الوحدات المسموحة، نوع الترخيص، معرف الجهاز، إلخ

#### ملف `models/baseModels/License/License.ts`
- نموذج الترخيص
- يحتوي على دوال للتحقق من صحة الترخيص وانتهاء الصلاحية
- يحتوي على دوال لإنشاء معرف الجهاز والتحقق منه
- يحتوي على دوال لتفعيل وإلغاء تفعيل الترخيص

الدوال الرئيسية:
- `isValid()` - التحقق من صحة الترخيص
- `isExpired()` - التحقق من انتهاء صلاحية الترخيص
- `getDaysRemaining()` - الحصول على عدد الأيام المتبقية
- `isModuleAllowed(module)` - التحقق من الوحدة المسموحة
- `generateMachineId()` - إنشاء معرف الجهاز
- `validateMachineId()` - التحقق من معرف الجهاز
- `activate()` - تفعيل الترخيص
- `deactivate()` - إلغاء تفعيل الترخيص

#### ملف `src/utils/licenseManager.ts`
- خدمة إدارة الترخيص
- يحتوي على دوال لتحميل وتفعيل والتحقق من الترخيص
- يحتوي على دوال للتحقق من صلاحيات الوحدات وعدد المستخدمين

الدوال الرئيسية:
- `loadLicense()` - تحميل الترخيص
- `validateLicense()` - التحقق من الترخيص
- `activateLicense(licenseKey, companyName, contactEmail)` - تفعيل الترخيص
- `isModuleAllowed(module)` - التحقق من الوحدة المسموحة
- `checkUserLimit()` - التحقق من عدد المستخدمين
- `getLicenseInfo()` - الحصول على معلومات الترخيص
- `periodicCheck()` - التحقق الدوري من الترخيص
- `deactivateLicense()` - إلغاء تفعيل الترخيص

#### ملف `src/pages/LicenseActivation.vue`
- صفحة تفعيل الترخيص
- يحتوي على نموذج لإدخال مفتاح الترخيص واسم الشركة والبريد الإلكتروني
- يحتوي على معلومات التواصل ومفاتيح الترخيص التجريبية

#### ملف `src/pages/LicenseInfo.vue`
- صفحة معلومات الترخيص
- يعرض تفاصيل الترخيص الحالي وحالته
- يعرض الوحدات المسموحة وإحصائيات الاستخدام
- يحتوي على خيار لإلغاء تفعيل الترخيص

## مفاتيح الترخيص التجريبية

يمكن استخدام المفاتيح التالية للاختبار:

1. `TRIAL-2024-001` - ترخيص تجريبي (5 مستخدمين، وحدات محدودة، صالح حتى 2024-12-31)
2. `STANDARD-2024-001` - ترخيص قياسي (10 مستخدمين، معظم الوحدات، صالح حتى 2025-12-31)
3. `PREMIUM-2024-001` - ترخيص مميز (25 مستخدم، جميع الوحدات، صالح حتى 2025-12-31)
4. `ENTERPRISE-2024-001` - ترخيص مؤسسي (100 مستخدم، جميع الوحدات، صالح حتى 2026-12-31)

## كيفية إضافة مفاتيح ترخيص جديدة

لإضافة مفاتيح ترخيص جديدة، قم بتعديل دالة `validateLicenseKey` في ملف `licenseManager.ts`:

```javascript
const validLicenses = {
  'NEW-LICENSE-KEY': {
    issuedDate: new Date('2024-01-01'),
    expiryDate: new Date('2025-12-31'),
    maxUsers: 20,
    licenseType: 'standard',
    allowedModules: ['sales', 'purchases', 'inventory'],
  },
  // المزيد من المفاتيح
};
```

## ملاحظات هامة

### نظام الصلاحيات
1. نظام الصلاحيات يعتمد حاليًا على `localStorage` بدلاً من قاعدة البيانات.
2. لتحسين النظام، يمكن تسجيل مخطط `UserPermission` في قاعدة البيانات واستخدامه بدلاً من `localStorage`.
3. يجب تسجيل الخروج وإعادة تسجيل الدخول بعد تغيير صلاحيات المستخدم لتطبيق التغييرات.
4. المستخدم `admin` لديه جميع الصلاحيات بغض النظر عن الإعدادات.
5. المستخدمون ذوو الدور `Admin` لديهم جميع الصلاحيات بغض النظر عن الإعدادات.

### نظام الترخيص
1. يتم التحقق من الترخيص عند كل تنقل في التطبيق.
2. يتم التحقق الدوري من الترخيص كل 24 ساعة.
3. يتم ربط الترخيص بمعرف الجهاز لمنع الاستخدام على أجهزة متعددة.
4. يتم التحقق من عدد المستخدمين عند إضافة مستخدم جديد.
5. يتم التحقق من صلاحيات الوحدات عند عرض القائمة الجانبية.
6. يظهر تحذير قبل انتهاء صلاحية الترخيص بـ 7 أيام.
