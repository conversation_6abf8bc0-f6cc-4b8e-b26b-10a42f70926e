import vue from '@vitejs/plugin-vue';
import path from 'path';
import { defineConfig } from 'vite';

/**
 * This vite config file is used only for dev mode, i.e.
 * to create a serve build modules of the source code
 * which will be rendered by electron.
 *
 * For building the project, vite is used programmatically
 * see build/scripts/build.mjs for this.
 */

export default () => {
  // استخدام المتغيرات البيئية التي تم تعيينها في dev.mjs
  // استخدام المنفذ 5173 كمنفذ افتراضي (منفذ Vite الافتراضي)
  let port = parseInt(process.env.VITE_PORT || '5173');
  let host = process.env.VITE_HOST || '0.0.0.0';
  let displayHost = process.env.VITE_DISPLAY_HOST || 'localhost';

  console.log(`Vite config using port: ${port}`);

  return defineConfig({
    server: {
      host,
      port,
      strictPort: false, // تغيير إلى false للسماح باستخدام منفذ بديل إذا كان المنفذ الأصلي مشغولاً
      hmr: {
        protocol: 'ws',
        host: displayHost,
        port: port
      },
      // إضافة إعدادات CORS للسماح بالوصول إلى الملفات
      cors: true,
      // إضافة إعدادات للتعامل مع الأخطاء
      watch: {
        usePolling: true
      }
    },
    root: path.resolve(__dirname, './src'),
    plugins: [vue()],
    resolve: {
      alias: {
        vue: 'vue/dist/vue.esm-bundler.js',
        fyo: path.resolve(__dirname, './fyo'),
        src: path.resolve(__dirname, './src'),
        schemas: path.resolve(__dirname, './schemas'),
        backend: path.resolve(__dirname, './backend'),
        models: path.resolve(__dirname, './models'),
        utils: path.resolve(__dirname, './utils'),
        regional: path.resolve(__dirname, './regional'),
        reports: path.resolve(__dirname, './reports'),
        dummy: path.resolve(__dirname, './dummy'),
        fixtures: path.resolve(__dirname, './fixtures'),
      },
    },
  });
};
