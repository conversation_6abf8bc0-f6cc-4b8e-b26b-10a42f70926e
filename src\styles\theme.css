/* ثيم النظام المحاسبي العصري - Modern Accounting Theme */

/* متغيرات الثيم الأساسية */
:root {
  /* الألوان الأساسية */
  --primary-50: #F0F9FF;
  --primary-500: #0EA5E9;
  --primary-600: #0284C7;
  --primary-700: #0369A1;
  
  /* ألوان الشريط الجانبي */
  --sidebar-bg: #1E293B;
  --sidebar-text: #F8FAFC;
  --sidebar-hover: #334155;
  --sidebar-active: #0EA5E9;
  
  /* ألوان المحتوى */
  --content-bg: #FEFEFE;
  --content-secondary: #F8FAFC;
  --content-border: #E2E8F0;
  
  /* الظلال والتأثيرات */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  
  /* الانتقالات */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}

/* ثيم Modern Blue */
[data-theme="modern"] {
  --sidebar-bg: #1E293B;
  --sidebar-text: #F8FAFC;
  --sidebar-hover: #334155;
  --sidebar-active: #0EA5E9;
  --content-bg: #FEFEFE;
  --content-secondary: #F8FAFC;
}

/* ثيم Dark Professional */
[data-theme="dark"] {
  --sidebar-bg: #111827;
  --sidebar-text: #F9FAFB;
  --sidebar-hover: #374151;
  --sidebar-active: #3B82F6;
  --content-bg: #1F2937;
  --content-secondary: #374151;
}

/* ثيم Ocean Breeze */
[data-theme="ocean"] {
  --sidebar-bg: #1E3A8A;
  --sidebar-text: #DBEAFE;
  --sidebar-hover: #3730A3;
  --sidebar-active: #06B6D4;
  --content-bg: #EFF6FF;
  --content-secondary: #DBEAFE;
}

/* ثيم Forest Green */
[data-theme="forest"] {
  --sidebar-bg: #166534;
  --sidebar-text: #DCFCE7;
  --sidebar-hover: #15803D;
  --sidebar-active: #14B8A6;
  --content-bg: #F0FDF4;
  --content-secondary: #DCFCE7;
}

/* ثيم Sunset Orange */
[data-theme="sunset"] {
  --sidebar-bg: #C2410C;
  --sidebar-text: #FFF7ED;
  --sidebar-hover: #EA580C;
  --sidebar-active: #EF4444;
  --content-bg: #FFF7ED;
  --content-secondary: #FFEDD5;
}

/* تصميم الشريط الجانبي */
.sidebar-container {
  background: var(--sidebar-bg) !important;
  color: var(--sidebar-text) !important;
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
}

.sidebar-item {
  color: var(--sidebar-text) !important;
  transition: all var(--transition-fast);
  border-radius: 8px;
  margin: 4px 8px;
  padding: 12px 16px;
}

.sidebar-item:hover {
  background: var(--sidebar-hover) !important;
  transform: translateX(4px);
  box-shadow: var(--shadow-md);
}

.sidebar-item.active {
  background: var(--sidebar-active) !important;
  color: white !important;
  box-shadow: var(--shadow-md);
}

.sidebar-item .icon {
  transition: all var(--transition-fast);
}

.sidebar-item:hover .icon {
  transform: scale(1.1);
}

/* تصميم المحتوى الرئيسي */
.main-content {
  background: var(--content-bg) !important;
  min-height: 100vh;
  transition: all var(--transition-normal);
}

.content-card {
  background: var(--content-secondary) !important;
  border: 1px solid var(--content-border);
  border-radius: 12px;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

.content-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

/* تصميم الأزرار */
.btn-primary {
  background: var(--primary-600) !important;
  border-color: var(--primary-600) !important;
  transition: all var(--transition-fast);
}

.btn-primary:hover {
  background: var(--primary-700) !important;
  border-color: var(--primary-700) !important;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* تصميم النماذج */
.form-input {
  border: 2px solid var(--content-border);
  border-radius: 8px;
  transition: all var(--transition-fast);
  background: var(--content-bg);
}

.form-input:focus {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
  outline: none;
}

/* تصميم الجداول */
.table-container {
  background: var(--content-secondary);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.table-header {
  background: var(--primary-50);
  color: var(--primary-700);
  font-weight: 600;
}

.table-row {
  transition: all var(--transition-fast);
}

.table-row:hover {
  background: var(--primary-50);
  transform: scale(1.01);
}

/* تصميم البطاقات */
.dashboard-card {
  background: var(--content-secondary);
  border-radius: 16px;
  padding: 24px;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
  border: 1px solid var(--content-border);
}

.dashboard-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-4px);
}

/* تصميم الإشعارات */
.notification {
  border-radius: 12px;
  padding: 16px;
  margin: 8px 0;
  transition: all var(--transition-fast);
}

.notification.success {
  background: #DCFCE7;
  border: 1px solid #16A34A;
  color: #15803D;
}

.notification.warning {
  background: #FEF3C7;
  border: 1px solid #D97706;
  color: #92400E;
}

.notification.error {
  background: #FEE2E2;
  border: 1px solid #DC2626;
  color: #991B1B;
}

/* تصميم القوائم المنسدلة */
.dropdown-menu {
  background: var(--content-secondary);
  border: 1px solid var(--content-border);
  border-radius: 12px;
  box-shadow: var(--shadow-xl);
  padding: 8px;
}

.dropdown-item {
  border-radius: 8px;
  padding: 12px 16px;
  transition: all var(--transition-fast);
}

.dropdown-item:hover {
  background: var(--primary-50);
  color: var(--primary-700);
}

/* تصميم شريط التنقل العلوي */
.top-navbar {
  background: var(--content-secondary);
  border-bottom: 1px solid var(--content-border);
  box-shadow: var(--shadow-sm);
  backdrop-filter: blur(10px);
}

/* تأثيرات الحركة */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* تصميم متجاوب */
@media (max-width: 768px) {
  .sidebar-container {
    transform: translateX(-100%);
  }
  
  .sidebar-container.open {
    transform: translateX(0);
  }
  
  .main-content {
    margin-left: 0 !important;
  }
}

/* تحسينات إضافية */
.glass-effect {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.gradient-bg {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-700));
}

.text-gradient {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-700));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* تخصيص شريط التمرير */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--content-border);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--primary-500);
  border-radius: 4px;
  transition: all var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-600);
}
