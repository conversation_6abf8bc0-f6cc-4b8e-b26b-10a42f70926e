/* ثيم النظام المحاسبي العصري - Modern Accounting Theme */

/* متغيرات الثيم الأساسية */
:root {
  /* الألوان الأساسية */
  --primary-50: #F0F9FF;
  --primary-500: #0EA5E9;
  --primary-600: #0284C7;
  --primary-700: #0369A1;
  
  /* ألوان الشريط الجانبي */
  --sidebar-bg: #1E293B;
  --sidebar-text: #F8FAFC;
  --sidebar-hover: #334155;
  --sidebar-active: #0EA5E9;
  
  /* ألوان المحتوى */
  --content-bg: #FEFEFE;
  --content-secondary: #F8FAFC;
  --content-border: #E2E8F0;
  
  /* الظلال والتأثيرات */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  
  /* الانتقالات */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}

/* ثيم Modern Blue */
[data-theme="modern"] {
  --sidebar-bg: #1E293B;
  --sidebar-text: #F8FAFC;
  --sidebar-hover: #334155;
  --sidebar-active: #0EA5E9;
  --content-bg: #FEFEFE;
  --content-secondary: #F8FAFC;
  --text-primary: #1E293B;
  --text-secondary: #64748B;
  --text-accent: #0EA5E9;
  --text-muted: #94A3B8;
}

/* ثيم Dark Professional */
[data-theme="dark"] {
  --sidebar-bg: #111827;
  --sidebar-text: #F9FAFB;
  --sidebar-hover: #374151;
  --sidebar-active: #3B82F6;
  --content-bg: #1F2937;
  --content-secondary: #374151;
  --text-primary: #F9FAFB;
  --text-secondary: #D1D5DB;
  --text-accent: #60A5FA;
  --text-muted: #9CA3AF;
}

/* ثيم Ocean Breeze */
[data-theme="ocean"] {
  --sidebar-bg: #1E3A8A;
  --sidebar-text: #DBEAFE;
  --sidebar-hover: #3730A3;
  --sidebar-active: #06B6D4;
  --content-bg: #EFF6FF;
  --content-secondary: #DBEAFE;
  --text-primary: #1E3A8A;
  --text-secondary: #3730A3;
  --text-accent: #06B6D4;
  --text-muted: #64748B;
}

/* ثيم Forest Green */
[data-theme="forest"] {
  --sidebar-bg: #166534;
  --sidebar-text: #DCFCE7;
  --sidebar-hover: #15803D;
  --sidebar-active: #14B8A6;
  --content-bg: #F0FDF4;
  --content-secondary: #DCFCE7;
  --text-primary: #166534;
  --text-secondary: #15803D;
  --text-accent: #14B8A6;
  --text-muted: #6B7280;
}

/* ثيم Sunset Orange */
[data-theme="sunset"] {
  --sidebar-bg: #C2410C;
  --sidebar-text: #FFF7ED;
  --sidebar-hover: #EA580C;
  --sidebar-active: #EF4444;
  --content-bg: #FFF7ED;
  --content-secondary: #FFEDD5;
  --text-primary: #C2410C;
  --text-secondary: #EA580C;
  --text-accent: #EF4444;
  --text-muted: #78716C;
}

/* ثيم Sky Blue - سماوي هادئ */
[data-theme="skyBlue"] {
  --sidebar-bg: #075985;
  --sidebar-text: #F0F9FF;
  --sidebar-hover: #0369A1;
  --sidebar-active: #0EA5E9;
  --content-bg: #F0F9FF;
  --content-secondary: #E0F2FE;
  --content-border: #BAE6FD;
  --text-primary: #075985;
  --text-secondary: #0369A1;
  --text-accent: #0EA5E9;
  --text-muted: #64748B;
}

/* ثيم Light Blue - أزرق فاتح */
[data-theme="lightBlue"] {
  --sidebar-bg: #0369A1;
  --sidebar-text: #F0F9FF;
  --sidebar-hover: #0284C7;
  --sidebar-active: #38BDF8;
  --content-bg: #F0F9FF;
  --content-secondary: #E0F2FE;
  --content-border: #BAE6FD;
  --text-primary: #0369A1;
  --text-secondary: #0284C7;
  --text-accent: #0EA5E9;
  --text-muted: #64748B;
}

/* تصميم الشريط الجانبي */
.sidebar-container {
  background: var(--sidebar-bg) !important;
  color: var(--sidebar-text) !important;
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
}

.sidebar-item {
  color: var(--sidebar-text) !important;
  transition: all var(--transition-fast);
  border-radius: 8px;
  margin: 4px 8px;
  padding: 12px 16px;
}

.sidebar-item:hover {
  background: var(--sidebar-hover) !important;
  transform: translateX(4px);
  box-shadow: var(--shadow-md);
}

.sidebar-item.active {
  background: var(--sidebar-active) !important;
  color: white !important;
  box-shadow: var(--shadow-md);
}

.sidebar-item .icon {
  transition: all var(--transition-fast);
}

.sidebar-item:hover .icon {
  transform: scale(1.1);
}

/* تصميم المحتوى الرئيسي */
.main-content {
  background: var(--content-bg) !important;
  min-height: 100vh;
  transition: all var(--transition-normal);
  color: var(--text-primary);
}

/* تحسين الخطوط حسب الثيم */
.theme-text-primary {
  color: var(--text-primary) !important;
}

.theme-text-secondary {
  color: var(--text-secondary) !important;
}

.theme-text-accent {
  color: var(--text-accent) !important;
}

.theme-text-muted {
  color: var(--text-muted) !important;
}

/* تحسين العناوين */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-primary);
  font-weight: 600;
}

/* تحسين النصوص العادية */
p, span, div {
  color: var(--text-secondary);
}

/* تحسين الروابط */
a {
  color: var(--text-accent);
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--text-primary);
}

.content-card {
  background: var(--content-secondary) !important;
  border: 1px solid var(--content-border);
  border-radius: 12px;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
  color: var(--text-primary);
}

.content-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.content-card h1, .content-card h2, .content-card h3,
.content-card h4, .content-card h5, .content-card h6 {
  color: var(--text-primary);
}

.content-card p, .content-card span {
  color: var(--text-secondary);
}

/* تصميم الأزرار المحسن */
.btn-primary {
  background: linear-gradient(135deg, var(--text-accent), var(--text-secondary)) !important;
  border: none !important;
  color: white !important;
  font-weight: 600;
  padding: 12px 24px;
  border-radius: 8px;
  transition: all var(--transition-fast);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-size: 14px;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  filter: brightness(1.1);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* أزرار ثانوية */
.btn-secondary {
  background: var(--content-bg) !important;
  border: 2px solid var(--text-accent) !important;
  color: var(--text-accent) !important;
  font-weight: 600;
  padding: 10px 22px;
  border-radius: 8px;
  transition: all var(--transition-fast);
}

.btn-secondary:hover {
  background: var(--text-accent) !important;
  color: white !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* أزرار صغيرة */
.btn-sm {
  padding: 8px 16px;
  font-size: 12px;
  border-radius: 6px;
}

/* أزرار كبيرة */
.btn-lg {
  padding: 16px 32px;
  font-size: 16px;
  border-radius: 10px;
}

/* تصميم النماذج المحسن */
.form-input {
  border: 2px solid var(--content-border);
  border-radius: 8px;
  transition: all var(--transition-fast);
  background: var(--content-bg);
  color: var(--text-primary);
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.form-input:focus {
  border-color: var(--text-accent);
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1), 0 1px 3px rgba(0, 0, 0, 0.1);
  outline: none;
  background: white;
}

.form-input:hover {
  border-color: var(--text-secondary);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-input::placeholder {
  color: var(--text-muted);
  font-weight: 400;
}

/* تحسين labels */
label {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 6px;
  display: block;
}

/* تحسين select boxes */
select.form-input {
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
}

/* تحسين textarea */
textarea.form-input {
  resize: vertical;
  min-height: 80px;
}

/* تحسين form groups */
.form-group {
  margin-bottom: 20px;
}

.form-group.required label::after {
  content: ' *';
  color: #EF4444;
  font-weight: bold;
}

/* تصميم الجداول المحسن */
.table-container {
  background: var(--content-bg);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--content-border);
}

.table-header {
  background: linear-gradient(135deg, var(--text-accent), var(--text-secondary));
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.table-header th {
  padding: 16px 12px;
  text-align: right;
  border-bottom: none;
  color: white;
  font-weight: 600;
}

.table-row {
  transition: all var(--transition-fast);
  color: var(--text-secondary);
  border-bottom: 1px solid var(--content-border);
}

.table-row:hover {
  background: var(--content-secondary);
  color: var(--text-primary);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table-row:nth-child(even) {
  background: rgba(var(--content-secondary), 0.3);
}

.table-row td {
  padding: 14px 12px;
  color: inherit;
  font-size: 14px;
  font-weight: 500;
}

.table-row th {
  color: white;
}

/* تحسين جداول الفواتير */
.invoice-table {
  background: white;
  border: 2px solid var(--content-border);
  border-radius: 8px;
  overflow: hidden;
  margin: 20px 0;
}

.invoice-table .table-header {
  background: var(--text-accent);
  color: white;
}

.invoice-table .table-row {
  border-bottom: 1px solid #E5E7EB;
}

.invoice-table .table-row:last-child {
  border-bottom: none;
}

.invoice-total-row {
  background: var(--content-secondary) !important;
  font-weight: 700;
  color: var(--text-primary) !important;
  border-top: 2px solid var(--text-accent);
}

/* تصميم البطاقات المحسن */
.dashboard-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all var(--transition-normal);
  border: 1px solid var(--content-border);
  color: var(--text-primary);
  position: relative;
  overflow: hidden;
}

.dashboard-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--text-accent), var(--text-secondary));
}

.dashboard-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transform: translateY(-4px);
  border-color: var(--text-accent);
}

.dashboard-card h1, .dashboard-card h2, .dashboard-card h3,
.dashboard-card h4, .dashboard-card h5, .dashboard-card h6 {
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.dashboard-card p, .dashboard-card span {
  color: var(--text-secondary);
  font-weight: 500;
}

.dashboard-card .accent-text {
  color: var(--text-accent);
  font-weight: 700;
  font-size: 1.1em;
}

/* بطاقات الإحصائيات */
.stat-card {
  background: linear-gradient(135deg, var(--content-bg), var(--content-secondary));
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  border: 1px solid var(--content-border);
  transition: all var(--transition-fast);
}

.stat-card:hover {
  transform: scale(1.02);
  box-shadow: 0 8px 12px rgba(0, 0, 0, 0.1);
}

.stat-card .stat-number {
  font-size: 2rem;
  font-weight: 800;
  color: var(--text-accent);
  margin-bottom: 8px;
}

.stat-card .stat-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-weight: 600;
}

/* تصميم الإشعارات */
.notification {
  border-radius: 12px;
  padding: 16px;
  margin: 8px 0;
  transition: all var(--transition-fast);
}

.notification.success {
  background: #DCFCE7;
  border: 1px solid #16A34A;
  color: #15803D;
}

.notification.warning {
  background: #FEF3C7;
  border: 1px solid #D97706;
  color: #92400E;
}

.notification.error {
  background: #FEE2E2;
  border: 1px solid #DC2626;
  color: #991B1B;
}

/* تصميم القوائم المنسدلة */
.dropdown-menu {
  background: var(--content-secondary);
  border: 1px solid var(--content-border);
  border-radius: 12px;
  box-shadow: var(--shadow-xl);
  padding: 8px;
}

.dropdown-item {
  border-radius: 8px;
  padding: 12px 16px;
  transition: all var(--transition-fast);
}

.dropdown-item:hover {
  background: var(--primary-50);
  color: var(--primary-700);
}

/* تصميم شريط التنقل العلوي */
.top-navbar {
  background: var(--content-secondary);
  border-bottom: 1px solid var(--content-border);
  box-shadow: var(--shadow-sm);
  backdrop-filter: blur(10px);
}

/* تأثيرات الحركة */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* تصميم متجاوب */
@media (max-width: 768px) {
  .sidebar-container {
    transform: translateX(-100%);
  }
  
  .sidebar-container.open {
    transform: translateX(0);
  }
  
  .main-content {
    margin-left: 0 !important;
  }
}

/* تحسينات إضافية */
.glass-effect {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.gradient-bg {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-700));
}

.text-gradient {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-700));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* تخصيص شريط التمرير */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--content-border);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--primary-500);
  border-radius: 4px;
  transition: all var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-accent);
}

/* تحسينات خاصة للفواتير وصفحات المبيعات */
.invoice-header {
  background: linear-gradient(135deg, var(--text-accent), var(--text-secondary));
  color: white;
  padding: 24px;
  border-radius: 12px 12px 0 0;
  margin-bottom: 0;
}

.invoice-header h1, .invoice-header h2 {
  color: white;
  margin: 0;
  font-weight: 700;
}

.invoice-body {
  background: white;
  padding: 24px;
  border: 1px solid var(--content-border);
  border-top: none;
  border-radius: 0 0 12px 12px;
}

.invoice-section {
  margin-bottom: 24px;
  padding: 16px;
  background: var(--content-secondary);
  border-radius: 8px;
  border-left: 4px solid var(--text-accent);
}

.invoice-section h3 {
  color: var(--text-primary);
  margin-bottom: 12px;
  font-weight: 600;
}

/* تحسين حقول البحث */
.search-input {
  background: white;
  border: 2px solid var(--content-border);
  border-radius: 25px;
  padding: 12px 20px 12px 45px;
  font-size: 14px;
  transition: all var(--transition-fast);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m21 21-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z'/%3e%3c/svg%3e");
  background-position: 15px center;
  background-repeat: no-repeat;
  background-size: 20px;
}

.search-input:focus {
  border-color: var(--text-accent);
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
  outline: none;
}

/* تحسين القوائم المنسدلة */
.dropdown-menu {
  background: white;
  border: 1px solid var(--content-border);
  border-radius: 12px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  padding: 8px;
  margin-top: 4px;
}

.dropdown-item {
  border-radius: 8px;
  padding: 12px 16px;
  transition: all var(--transition-fast);
  color: var(--text-secondary);
  font-weight: 500;
}

.dropdown-item:hover {
  background: var(--content-secondary);
  color: var(--text-primary);
  transform: translateX(4px);
}

.dropdown-item.active {
  background: var(--text-accent);
  color: white;
}

/* تحسين التبويبات */
.tab-container {
  border-bottom: 2px solid var(--content-border);
  margin-bottom: 24px;
}

.tab-item {
  padding: 12px 24px;
  border-bottom: 3px solid transparent;
  color: var(--text-secondary);
  font-weight: 600;
  transition: all var(--transition-fast);
  cursor: pointer;
}

.tab-item:hover {
  color: var(--text-primary);
  background: var(--content-secondary);
}

.tab-item.active {
  color: var(--text-accent);
  border-bottom-color: var(--text-accent);
  background: var(--content-secondary);
}

/* تحسين الإشعارات */
.notification {
  border-radius: 12px;
  padding: 16px 20px;
  margin: 8px 0;
  transition: all var(--transition-fast);
  border-left: 4px solid;
  font-weight: 500;
}

.notification.success {
  background: #DCFCE7;
  border-left-color: #16A34A;
  color: #15803D;
}

.notification.warning {
  background: #FEF3C7;
  border-left-color: #D97706;
  color: #92400E;
}

.notification.error {
  background: #FEE2E2;
  border-left-color: #DC2626;
  color: #991B1B;
}

.notification.info {
  background: var(--content-secondary);
  border-left-color: var(--text-accent);
  color: var(--text-primary);
}

/* تحسين شريط التقدم */
.progress-bar {
  background: var(--content-border);
  border-radius: 10px;
  height: 8px;
  overflow: hidden;
}

.progress-fill {
  background: linear-gradient(90deg, var(--text-accent), var(--text-secondary));
  height: 100%;
  border-radius: 10px;
  transition: width var(--transition-normal);
}
