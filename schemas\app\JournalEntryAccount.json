{"name": "JournalEntryAccount", "label": "Journal Entry Account", "isChild": true, "fields": [{"fieldname": "account", "label": "Account", "placeholder": "Account", "fieldtype": "Link", "target": "Account", "required": true, "groupBy": "rootType"}, {"fieldname": "debit", "label": "Debit", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldname": "credit", "label": "Credit", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>"}], "tableFields": ["account", "debit", "credit"], "keywordFields": ["account"]}