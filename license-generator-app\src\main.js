const { app, BrowserWindow, ipcMain, dialog, clipboard } = require('electron');
const path = require('path');
const fs = require('fs');
const os = require('os');
const crypto = require('crypto');

// متغيرات عامة
let mainWindow;
let licenseHistory = [];

// تعطيل تحذيرات الأمان في وضع التطوير
process.env['ELECTRON_DISABLE_SECURITY_WARNINGS'] = 'true';

// إنشاء النافذة الرئيسية
function createWindow() {
  mainWindow = new BrowserWindow({
    width: 900,
    height: 700,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    },
    icon: path.join(__dirname, '../assets/icon.png'),
    title: 'مولد التراخيص - أداة المطور',
    resizable: true,
    minimizable: true,
    maximizable: true,
    show: false
  });

  // تحميل الصفحة الرئيسية
  mainWindow.loadFile(path.join(__dirname, 'index.html'));

  // إظهار النافذة عند الانتهاء من التحميل
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    // تحميل سجل التراخيص
    loadLicenseHistory();
  });

  // إغلاق التطبيق عند إغلاق النافذة
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // فتح أدوات المطور في وضع التطوير
  if (process.argv.includes('--dev')) {
    mainWindow.webContents.openDevTools();
  }
}

// دالة إنشاء الرقم التسلسلي
function generateSerialNumber() {
  try {
    const networkInterfaces = os.networkInterfaces();
    const cpus = os.cpus();
    const platform = os.platform();
    const arch = os.arch();
    
    // جمع معلومات الجهاز
    const machineInfo = {
      platform,
      arch,
      cpuModel: cpus[0]?.model || '',
      totalMemory: os.totalmem(),
      hostname: os.hostname(),
      userInfo: os.userInfo().username,
    };
    
    // جمع عناوين MAC
    const macAddresses = [];
    if (networkInterfaces && typeof networkInterfaces === 'object') {
      Object.values(networkInterfaces).forEach((interfaces) => {
        if (Array.isArray(interfaces)) {
          interfaces.forEach((iface) => {
            if (iface && iface.mac && iface.mac !== '00:00:00:00:00:00') {
              macAddresses.push(iface.mac);
            }
          });
        }
      });
    }
    
    // أخذ أول عنوان MAC كمعرف أساسي
    const primaryMac = macAddresses.sort()[0] || 'NO-MAC';
    machineInfo.primaryMac = primaryMac;
    
    // إنشاء hash للمعلومات
    const hash = crypto.createHash('sha256');
    hash.update(JSON.stringify(machineInfo));
    const fullHash = hash.digest('hex');
    
    // تنسيق الرقم التسلسلي: XXXX-XXXX-XXXX-XXXX
    const serialParts = [
      fullHash.substring(0, 4).toUpperCase(),
      fullHash.substring(4, 8).toUpperCase(),
      fullHash.substring(8, 12).toUpperCase(),
      fullHash.substring(12, 16).toUpperCase(),
    ];
    
    return serialParts.join('-');
  } catch (error) {
    console.error('Error generating serial number:', error);
    // إرجاع رقم تسلسلي افتراضي في حالة الخطأ
    const timestamp = Date.now().toString(36).toUpperCase();
    return `ERR-${timestamp.substring(0, 4)}-${timestamp.substring(4, 8)}-${timestamp.substring(8, 12)}`;
  }
}

// دالة إنشاء مفتاح الترخيص
function generateLicenseKey(serialNumber, licenseType = 'trial', durationDays = 7) {
  try {
    // إزالة الشرطات من الرقم التسلسلي
    const cleanSerial = serialNumber.replace(/-/g, '');
    
    // إنشاء بيانات الترخيص
    const licenseData = {
      serial: cleanSerial,
      type: licenseType,
      duration: durationDays,
      timestamp: Date.now(),
    };
    
    // إنشاء hash للبيانات
    const hash = crypto.createHash('sha256');
    hash.update(JSON.stringify(licenseData));
    const licenseHash = hash.digest('hex');
    
    // تنسيق مفتاح الترخيص
    let prefix = '';
    switch (licenseType) {
      case 'trial':
        prefix = 'TRL';
        break;
      case 'permanent':
        prefix = 'PRM';
        break;
      default:
        prefix = 'GEN';
    }
    
    // تكوين المفتاح: PREFIX-XXXX-XXXX-XXXX-XXXX
    const keyParts = [
      prefix,
      licenseHash.substring(0, 4).toUpperCase(),
      licenseHash.substring(4, 8).toUpperCase(),
      licenseHash.substring(8, 12).toUpperCase(),
      licenseHash.substring(12, 16).toUpperCase(),
    ];
    
    return keyParts.join('-');
  } catch (error) {
    console.error('Error generating license key:', error);
    throw new Error('فشل في إنشاء مفتاح الترخيص');
  }
}

// دالة تحميل سجل التراخيص
function loadLicenseHistory() {
  try {
    const historyPath = path.join(os.homedir(), 'license-generator-history.json');
    if (fs.existsSync(historyPath)) {
      const data = fs.readFileSync(historyPath, 'utf8');
      licenseHistory = JSON.parse(data);
    }
  } catch (error) {
    console.error('Error loading license history:', error);
    licenseHistory = [];
  }
}

// دالة حفظ سجل التراخيص
function saveLicenseHistory() {
  try {
    const historyPath = path.join(os.homedir(), 'license-generator-history.json');
    // الاحتفاظ بآخر 100 ترخيص فقط
    const historyToSave = licenseHistory.slice(0, 100);
    fs.writeFileSync(historyPath, JSON.stringify(historyToSave, null, 2));
  } catch (error) {
    console.error('Error saving license history:', error);
  }
}

// معالجات IPC
ipcMain.handle('get-serial-number', () => {
  return generateSerialNumber();
});

ipcMain.handle('generate-license', (event, data) => {
  try {
    const { serialNumber, licenseType, duration, clientName, notes } = data;
    
    // إنشاء مفتاح الترخيص
    const licenseKey = generateLicenseKey(serialNumber, licenseType, duration);
    
    // إنشاء كائن الترخيص
    const license = {
      key: licenseKey,
      serialNumber: serialNumber,
      type: licenseType,
      duration: duration,
      clientName: clientName || '',
      notes: notes || '',
      createdAt: new Date().toISOString(),
    };
    
    // إضافة إلى السجل
    licenseHistory.unshift(license);
    saveLicenseHistory();
    
    return { success: true, license };
  } catch (error) {
    console.error('Error generating license:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-license-history', () => {
  return licenseHistory;
});

ipcMain.handle('copy-to-clipboard', (event, text) => {
  try {
    clipboard.writeText(text);
    return { success: true };
  } catch (error) {
    console.error('Error copying to clipboard:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('export-history', async () => {
  try {
    const result = await dialog.showSaveDialog(mainWindow, {
      title: 'تصدير سجل التراخيص',
      defaultPath: 'license-history.json',
      filters: [
        { name: 'JSON Files', extensions: ['json'] },
        { name: 'All Files', extensions: ['*'] }
      ]
    });
    
    if (!result.canceled && result.filePath) {
      fs.writeFileSync(result.filePath, JSON.stringify(licenseHistory, null, 2));
      return { success: true, path: result.filePath };
    }
    
    return { success: false, error: 'تم إلغاء العملية' };
  } catch (error) {
    console.error('Error exporting history:', error);
    return { success: false, error: error.message };
  }
});

// أحداث التطبيق
app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
