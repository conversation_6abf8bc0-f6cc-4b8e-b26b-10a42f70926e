<template>
  <div
    class="
      flex
      items-center
      justify-center
      h-full
      bg-gray-50
      dark:bg-gray-900
    "
  >
    <div
      class="
        w-full
        max-w-md
        p-8
        bg-white
        dark:bg-gray-800
        rounded-lg
        shadow-lg
        border
        dark:border-gray-700
      "
    >
      <div class="text-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800 dark:text-white">
          {{ t`تفعيل الترخيص` }}
        </h1>
        <p class="text-gray-600 dark:text-gray-400 mt-2">
          {{ t`يرجى إدخال مفتاح الترخيص لتفعيل النظام` }}
        </p>
      </div>

      <!-- عرض الرقم التسلسلي للجهاز - قسم جديد -->
      <div class="mb-6 p-4 bg-blue-50 dark:bg-blue-900 rounded-md">
        <h3 class="text-lg font-medium text-blue-800 dark:text-blue-200 mb-2">
          {{ t`الرقم التسلسلي للجهاز` }}
        </h3>
        <p class="text-sm text-blue-700 dark:text-blue-300 mb-2">
          {{ t`أرسل هذا الرقم للمطور للحصول على مفتاح الترخيص:` }}
        </p>
        <div class="flex items-center space-x-2 space-x-reverse">
          <input
            :value="serialNumber"
            readonly
            class="
              flex-1
              px-3
              py-2
              border
              rounded-md
              bg-gray-100
              dark:bg-gray-700
              dark:border-gray-600
              dark:text-white
              font-mono
              text-center
              text-sm
            "
          />
          <button
            @click="copySerialNumber"
            class="
              px-3
              py-2
              bg-blue-600
              hover:bg-blue-700
              text-white
              rounded-md
              text-sm
            "
          >
            {{ t`نسخ` }}
          </button>
        </div>
      </div>

      <div v-if="errorMessage" class="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
        {{ errorMessage }}
      </div>

      <div v-if="successMessage" class="mb-4 p-3 bg-green-100 text-green-700 rounded-md">
        {{ successMessage }}
      </div>

      <form @submit.prevent="activateLicense">
        <div class="mb-4">
          <label
            for="licenseKey"
            class="block text-gray-700 dark:text-gray-300 mb-2"
          >
            {{ t`مفتاح الترخيص` }}
          </label>
          <input
            id="licenseKey"
            v-model="licenseForm.licenseKey"
            type="text"
            class="
              w-full
              px-3
              py-2
              border
              rounded-md
              focus:outline-none
              focus:ring-2
              focus:ring-blue-500
              dark:bg-gray-700
              dark:border-gray-600
              dark:text-white
              font-mono
              text-center
              uppercase
            "
            placeholder="XXXX-XXXX-XXXX-XXXX"
            required
          />
        </div>

        <div class="mb-4">
          <label
            for="companyName"
            class="block text-gray-700 dark:text-gray-300 mb-2"
          >
            {{ t`اسم الشركة` }}
          </label>
          <input
            id="companyName"
            v-model="licenseForm.companyName"
            type="text"
            class="
              w-full
              px-3
              py-2
              border
              rounded-md
              focus:outline-none
              focus:ring-2
              focus:ring-blue-500
              dark:bg-gray-700
              dark:border-gray-600
              dark:text-white
            "
            required
          />
        </div>

        <div class="mb-4">
          <label
            for="contactEmail"
            class="block text-gray-700 dark:text-gray-300 mb-2"
          >
            {{ t`البريد الإلكتروني للتواصل` }}
          </label>
          <input
            id="contactEmail"
            v-model="licenseForm.contactEmail"
            type="email"
            class="
              w-full
              px-3
              py-2
              border
              rounded-md
              focus:outline-none
              focus:ring-2
              focus:ring-blue-500
              dark:bg-gray-700
              dark:border-gray-600
              dark:text-white
            "
            required
          />
        </div>

        <button
          type="submit"
          class="
            w-full
            py-2
            px-4
            bg-blue-600
            hover:bg-blue-700
            text-white
            rounded-md
            focus:outline-none
            focus:ring-2
            focus:ring-blue-500
            focus:ring-offset-2
          "
          :disabled="isLoading"
        >
          <span v-if="isLoading">{{ t`جاري التفعيل...` }}</span>
          <span v-else>{{ t`تفعيل الترخيص` }}</span>
        </button>
      </form>

      <div class="mt-6 text-center">
        <p class="text-sm text-gray-600 dark:text-gray-400">
          {{ t`لا تملك مفتاح ترخيص؟` }}
          <a
            href="#"
            @click="showContactInfo = true"
            class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
          >
            {{ t`تواصل معنا` }}
          </a>
        </p>
      </div>

      <!-- معلومات التواصل -->
      <div v-if="showContactInfo" class="mt-4 p-4 bg-blue-50 dark:bg-blue-900 rounded-md">
        <h3 class="text-lg font-medium text-blue-800 dark:text-blue-200 mb-2">
          {{ t`معلومات التواصل` }}
        </h3>
        <p class="text-sm text-blue-700 dark:text-blue-300">
          {{ t`للحصول على مفتاح ترخيص، يرجى التواصل معنا:` }}
        </p>
        <ul class="mt-2 text-sm text-blue-700 dark:text-blue-300">
          <li>{{ t`البريد الإلكتروني: <EMAIL>` }}</li>
          <li>{{ t`الهاتف: +966 XX XXX XXXX` }}</li>
          <li>{{ t`الموقع الإلكتروني: www.company.com` }}</li>
        </ul>
        <button
          @click="showContactInfo = false"
          class="mt-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
        >
          {{ t`إغلاق` }}
        </button>
      </div>

      <!-- معلومات الترخيص التجريبي - الكود الأصلي -->
      <!-- <div class="mt-4 p-4 bg-yellow-50 dark:bg-yellow-900 rounded-md">
        <h3 class="text-lg font-medium text-yellow-800 dark:text-yellow-200 mb-2">
          {{ t`ترخيص تجريبي` }}
        </h3>
        <p class="text-sm text-yellow-700 dark:text-yellow-300 mb-2">
          {{ t`يمكنك استخدام المفاتيح التجريبية التالية:` }}
        </p>
        <div class="space-y-1 text-xs font-mono">
          <div class="flex justify-between">
            <span>TRIAL-2024-001</span>
            <span class="text-yellow-600">{{ t`تجريبي` }}</span>
          </div>
          <div class="flex justify-between">
            <span>STANDARD-2024-001</span>
            <span class="text-blue-600">{{ t`قياسي` }}</span>
          </div>
          <div class="flex justify-between">
            <span>PREMIUM-2024-001</span>
            <span class="text-purple-600">{{ t`مميز` }}</span>
          </div>
          <div class="flex justify-between">
            <span>ENTERPRISE-2024-001</span>
            <span class="text-green-600">{{ t`مؤسسي` }}</span>
          </div>
        </div>
      </div> -->

      <!-- معلومات الترخيص الجديدة - الكود الجديد -->
      <div class="mt-4 p-4 bg-yellow-50 dark:bg-yellow-900 rounded-md">
        <h3 class="text-lg font-medium text-yellow-800 dark:text-yellow-200 mb-2">
          {{ t`أنواع التراخيص` }}
        </h3>
        <p class="text-sm text-yellow-700 dark:text-yellow-300 mb-2">
          {{ t`يمكنك الحصول على الأنواع التالية من التراخيص:` }}
        </p>
        <div class="space-y-2 text-sm">
          <div class="flex justify-between items-center">
            <span class="font-medium">{{ t`ترخيص تجريبي` }}</span>
            <span class="text-yellow-600 text-xs">{{ t`7 أيام - 3 مستخدمين` }}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="font-medium">{{ t`ترخيص دائم` }}</span>
            <span class="text-green-600 text-xs">{{ t`دائم - 50 مستخدم` }}</span>
          </div>
        </div>
        <div class="mt-3 p-2 bg-yellow-100 dark:bg-yellow-800 rounded text-xs">
          <p class="text-yellow-800 dark:text-yellow-200">
            {{ t`للحصول على مفتاح الترخيص، أرسل الرقم التسلسلي أعلاه إلى المطور` }}
          </p>
        </div>
      </div>

      <!-- أدوات المطور للاختبار - قسم جديد -->
      <div v-if="showDeveloperTools" class="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-md border-2 border-dashed border-gray-300 dark:border-gray-600">
        <h3 class="text-lg font-medium text-gray-800 dark:text-gray-200 mb-2">
          {{ t`أدوات المطور` }}
        </h3>
        <div class="space-y-2">
          <button
            @click="generateTrialKey"
            class="w-full px-3 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-md text-sm"
          >
            {{ t`إنشاء مفتاح تجريبي` }}
          </button>
          <button
            @click="generatePermanentKey"
            class="w-full px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md text-sm"
          >
            {{ t`إنشاء مفتاح دائم` }}
          </button>
          <div v-if="generatedKey" class="mt-2 p-2 bg-gray-100 dark:bg-gray-700 rounded">
            <p class="text-xs text-gray-600 dark:text-gray-400 mb-1">{{ t`المفتاح المُنشأ:` }}</p>
            <input
              :value="generatedKey"
              readonly
              class="w-full px-2 py-1 border rounded text-xs font-mono"
            />
          </div>
        </div>
      </div>

      <!-- زر إظهار أدوات المطور -->
      <div class="mt-4 text-center">
        <button
          @click="toggleDeveloperTools"
          class="text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
        >
          {{ showDeveloperTools ? t`إخفاء أدوات المطور` : t`إظهار أدوات المطور` }}
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { LicenseManager } from 'src/utils/licenseManager';
import { showToast } from 'src/utils/interactive';

export default defineComponent({
  name: 'LicenseActivation',
  data() {
    return {
      // البيانات الأصلية
      // licenseForm: {
      //   licenseKey: '',
      //   companyName: '',
      //   contactEmail: '',
      // },
      // errorMessage: '',
      // successMessage: '',
      // isLoading: false,
      // showContactInfo: false,

      // البيانات الجديدة مع الرقم التسلسلي
      licenseForm: {
        licenseKey: '',
        companyName: '',
        contactEmail: '',
      },
      errorMessage: '',
      successMessage: '',
      isLoading: false,
      showContactInfo: false,
      serialNumber: '', // الرقم التسلسلي للجهاز
      showDeveloperTools: false, // إظهار أدوات المطور
      generatedKey: '', // المفتاح المُنشأ
    };
  },
  async mounted() {
    // التحقق من وجود ترخيص نشط - الكود الأصلي
    // const licenseManager = LicenseManager.getInstance();
    // const licenseInfo = await licenseManager.getLicenseInfo();
    //
    // if (licenseInfo.isValid) {
    //   // إذا كان هناك ترخيص صالح، إعادة توجيه إلى الصفحة الرئيسية
    //   this.$router.push('/');
    // }

    // التحقق من وجود ترخيص نشط وإنشاء الرقم التسلسلي - الكود الجديد
    const licenseManager = LicenseManager.getInstance();

    // إنشاء الرقم التسلسلي للجهاز
    this.serialNumber = licenseManager.getSerialNumber();

    const licenseInfo = await licenseManager.getLicenseInfo();

    if (licenseInfo.isValid) {
      // إذا كان هناك ترخيص صالح، إعادة توجيه إلى الصفحة الرئيسية
      this.$router.push('/');
    }
  },
  methods: {
    // تفعيل الترخيص - الكود الأصلي
    // async activateLicense() {
    //   this.isLoading = true;
    //   this.errorMessage = '';
    //   this.successMessage = '';
    //
    //   try {
    //     const licenseManager = LicenseManager.getInstance();
    //     const success = await licenseManager.activateLicense(
    //       this.licenseForm.licenseKey.toUpperCase(),
    //       this.licenseForm.companyName,
    //       this.licenseForm.contactEmail
    //     );
    //
    //     if (success) {
    //       this.successMessage = this.t`تم تفعيل الترخيص بنجاح! سيتم إعادة توجيهك إلى النظام...`;
    //
    //       // إعادة توجيه إلى الصفحة الرئيسية بعد 2 ثانية
    //       setTimeout(() => {
    //         this.$router.push('/');
    //       }, 2000);
    //     } else {
    //       this.errorMessage = this.t`فشل في تفعيل الترخيص. يرجى التحقق من مفتاح الترخيص والمحاولة مرة أخرى.`;
    //     }
    //   } catch (error) {
    //     console.error('Error activating license:', error);
    //     this.errorMessage = this.t`حدث خطأ أثناء تفعيل الترخيص. يرجى المحاولة مرة أخرى.`;
    //   } finally {
    //     this.isLoading = false;
    //   }
    // },

    // تفعيل الترخيص - الكود الجديد مع الرقم التسلسلي
    async activateLicense() {
      this.isLoading = true;
      this.errorMessage = '';
      this.successMessage = '';

      try {
        const licenseManager = LicenseManager.getInstance();
        const success = await licenseManager.activateLicense(
          this.licenseForm.licenseKey.toUpperCase(),
          this.licenseForm.companyName,
          this.licenseForm.contactEmail,
          this.serialNumber // تمرير الرقم التسلسلي
        );

        if (success) {
          this.successMessage = this.t`تم تفعيل الترخيص بنجاح! سيتم إعادة توجيهك إلى النظام...`;

          // إعادة توجيه إلى الصفحة الرئيسية بعد 2 ثانية
          setTimeout(() => {
            this.$router.push('/');
          }, 2000);
        } else {
          this.errorMessage = this.t`فشل في تفعيل الترخيص. يرجى التحقق من مفتاح الترخيص والمحاولة مرة أخرى.`;
        }
      } catch (error) {
        console.error('Error activating license:', error);
        this.errorMessage = this.t`حدث خطأ أثناء تفعيل الترخيص. يرجى المحاولة مرة أخرى.`;
      } finally {
        this.isLoading = false;
      }
    },

    // نسخ الرقم التسلسلي - دالة جديدة
    async copySerialNumber() {
      try {
        await navigator.clipboard.writeText(this.serialNumber);
        showToast({
          message: this.t`تم نسخ الرقم التسلسلي`,
          type: 'success',
        });
      } catch (error) {
        console.error('Error copying serial number:', error);
        showToast({
          message: this.t`فشل في نسخ الرقم التسلسلي`,
          type: 'error',
        });
      }
    },

    // إظهار/إخفاء أدوات المطور - دالة جديدة
    toggleDeveloperTools() {
      this.showDeveloperTools = !this.showDeveloperTools;
      this.generatedKey = '';
    },

    // إنشاء مفتاح تجريبي - دالة جديدة للمطور
    generateTrialKey() {
      try {
        this.generatedKey = LicenseManager.createLicenseKey(this.serialNumber, 'trial', 7);
        showToast({
          message: this.t`تم إنشاء مفتاح تجريبي`,
          type: 'success',
        });
      } catch (error) {
        console.error('Error generating trial key:', error);
        showToast({
          message: this.t`فشل في إنشاء المفتاح التجريبي`,
          type: 'error',
        });
      }
    },

    // إنشاء مفتاح دائم - دالة جديدة للمطور
    generatePermanentKey() {
      try {
        this.generatedKey = LicenseManager.createLicenseKey(this.serialNumber, 'permanent');
        showToast({
          message: this.t`تم إنشاء مفتاح دائم`,
          type: 'success',
        });
      } catch (error) {
        console.error('Error generating permanent key:', error);
        showToast({
          message: this.t`فشل في إنشاء المفتاح الدائم`,
          type: 'error',
        });
      }
    },
  },
});
</script>
