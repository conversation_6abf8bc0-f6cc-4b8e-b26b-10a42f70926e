<template>
  <div
    class="
      flex
      items-center
      justify-center
      h-full
      bg-gray-50
      dark:bg-gray-900
    "
  >
    <div
      class="
        w-full
        max-w-md
        p-8
        bg-white
        dark:bg-gray-800
        rounded-lg
        shadow-lg
        border
        dark:border-gray-700
      "
    >
      <div class="text-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800 dark:text-white">
          {{ t`تفعيل الترخيص` }}
        </h1>
        <p class="text-gray-600 dark:text-gray-400 mt-2">
          {{ t`يرجى إدخال مفتاح الترخيص لتفعيل النظام` }}
        </p>
      </div>

      <div v-if="errorMessage" class="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
        {{ errorMessage }}
      </div>

      <div v-if="successMessage" class="mb-4 p-3 bg-green-100 text-green-700 rounded-md">
        {{ successMessage }}
      </div>

      <form @submit.prevent="activateLicense">
        <div class="mb-4">
          <label
            for="licenseKey"
            class="block text-gray-700 dark:text-gray-300 mb-2"
          >
            {{ t`مفتاح الترخيص` }}
          </label>
          <input
            id="licenseKey"
            v-model="licenseForm.licenseKey"
            type="text"
            class="
              w-full
              px-3
              py-2
              border
              rounded-md
              focus:outline-none
              focus:ring-2
              focus:ring-blue-500
              dark:bg-gray-700
              dark:border-gray-600
              dark:text-white
              font-mono
              text-center
              uppercase
            "
            placeholder="XXXX-XXXX-XXXX-XXXX"
            required
          />
        </div>

        <div class="mb-4">
          <label
            for="companyName"
            class="block text-gray-700 dark:text-gray-300 mb-2"
          >
            {{ t`اسم الشركة` }}
          </label>
          <input
            id="companyName"
            v-model="licenseForm.companyName"
            type="text"
            class="
              w-full
              px-3
              py-2
              border
              rounded-md
              focus:outline-none
              focus:ring-2
              focus:ring-blue-500
              dark:bg-gray-700
              dark:border-gray-600
              dark:text-white
            "
            required
          />
        </div>

        <div class="mb-4">
          <label
            for="contactEmail"
            class="block text-gray-700 dark:text-gray-300 mb-2"
          >
            {{ t`البريد الإلكتروني للتواصل` }}
          </label>
          <input
            id="contactEmail"
            v-model="licenseForm.contactEmail"
            type="email"
            class="
              w-full
              px-3
              py-2
              border
              rounded-md
              focus:outline-none
              focus:ring-2
              focus:ring-blue-500
              dark:bg-gray-700
              dark:border-gray-600
              dark:text-white
            "
            required
          />
        </div>

        <button
          type="submit"
          class="
            w-full
            py-2
            px-4
            bg-blue-600
            hover:bg-blue-700
            text-white
            rounded-md
            focus:outline-none
            focus:ring-2
            focus:ring-blue-500
            focus:ring-offset-2
          "
          :disabled="isLoading"
        >
          <span v-if="isLoading">{{ t`جاري التفعيل...` }}</span>
          <span v-else>{{ t`تفعيل الترخيص` }}</span>
        </button>
      </form>

      <div class="mt-6 text-center">
        <p class="text-sm text-gray-600 dark:text-gray-400">
          {{ t`لا تملك مفتاح ترخيص؟` }}
          <a
            href="#"
            @click="showContactInfo = true"
            class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
          >
            {{ t`تواصل معنا` }}
          </a>
        </p>
      </div>

      <!-- معلومات التواصل -->
      <div v-if="showContactInfo" class="mt-4 p-4 bg-blue-50 dark:bg-blue-900 rounded-md">
        <h3 class="text-lg font-medium text-blue-800 dark:text-blue-200 mb-2">
          {{ t`معلومات التواصل` }}
        </h3>
        <p class="text-sm text-blue-700 dark:text-blue-300">
          {{ t`للحصول على مفتاح ترخيص، يرجى التواصل معنا:` }}
        </p>
        <ul class="mt-2 text-sm text-blue-700 dark:text-blue-300">
          <li>{{ t`البريد الإلكتروني: <EMAIL>` }}</li>
          <li>{{ t`الهاتف: +966 XX XXX XXXX` }}</li>
          <li>{{ t`الموقع الإلكتروني: www.company.com` }}</li>
        </ul>
        <button
          @click="showContactInfo = false"
          class="mt-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
        >
          {{ t`إغلاق` }}
        </button>
      </div>

      <!-- معلومات الترخيص التجريبي -->
      <div class="mt-4 p-4 bg-yellow-50 dark:bg-yellow-900 rounded-md">
        <h3 class="text-lg font-medium text-yellow-800 dark:text-yellow-200 mb-2">
          {{ t`ترخيص تجريبي` }}
        </h3>
        <p class="text-sm text-yellow-700 dark:text-yellow-300 mb-2">
          {{ t`يمكنك استخدام المفاتيح التجريبية التالية:` }}
        </p>
        <div class="space-y-1 text-xs font-mono">
          <div class="flex justify-between">
            <span>TRIAL-2024-001</span>
            <span class="text-yellow-600">{{ t`تجريبي` }}</span>
          </div>
          <div class="flex justify-between">
            <span>STANDARD-2024-001</span>
            <span class="text-blue-600">{{ t`قياسي` }}</span>
          </div>
          <div class="flex justify-between">
            <span>PREMIUM-2024-001</span>
            <span class="text-purple-600">{{ t`مميز` }}</span>
          </div>
          <div class="flex justify-between">
            <span>ENTERPRISE-2024-001</span>
            <span class="text-green-600">{{ t`مؤسسي` }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { LicenseManager } from 'src/utils/licenseManager';
import { showToast } from 'src/utils/interactive';

export default defineComponent({
  name: 'LicenseActivation',
  data() {
    return {
      licenseForm: {
        licenseKey: '',
        companyName: '',
        contactEmail: '',
      },
      errorMessage: '',
      successMessage: '',
      isLoading: false,
      showContactInfo: false,
    };
  },
  async mounted() {
    // التحقق من وجود ترخيص نشط
    const licenseManager = LicenseManager.getInstance();
    const licenseInfo = await licenseManager.getLicenseInfo();

    if (licenseInfo.isValid) {
      // إذا كان هناك ترخيص صالح، إعادة توجيه إلى الصفحة الرئيسية
      this.$router.push('/');
    }
  },
  methods: {
    async activateLicense() {
      this.isLoading = true;
      this.errorMessage = '';
      this.successMessage = '';

      try {
        const licenseManager = LicenseManager.getInstance();
        const success = await licenseManager.activateLicense(
          this.licenseForm.licenseKey.toUpperCase(),
          this.licenseForm.companyName,
          this.licenseForm.contactEmail
        );

        if (success) {
          this.successMessage = this.t`تم تفعيل الترخيص بنجاح! سيتم إعادة توجيهك إلى النظام...`;
          
          // إعادة توجيه إلى الصفحة الرئيسية بعد 2 ثانية
          setTimeout(() => {
            this.$router.push('/');
          }, 2000);
        } else {
          this.errorMessage = this.t`فشل في تفعيل الترخيص. يرجى التحقق من مفتاح الترخيص والمحاولة مرة أخرى.`;
        }
      } catch (error) {
        console.error('Error activating license:', error);
        this.errorMessage = this.t`حدث خطأ أثناء تفعيل الترخيص. يرجى المحاولة مرة أخرى.`;
      } finally {
        this.isLoading = false;
      }
    },
  },
});
</script>
