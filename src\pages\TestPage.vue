<template>
  <div class="flex items-center justify-center h-full bg-blue-100">
    <div class="bg-white p-8 rounded-lg shadow-lg max-w-md w-full">
      <h1 class="text-2xl font-bold text-center mb-4">صفحة اختبار</h1>
      <p class="text-gray-600 mb-4">
        هذه صفحة اختبار بسيطة للتأكد من أن الخادم يعمل بشكل صحيح.
      </p>
      <div class="flex flex-col space-y-4">
        <button
          @click="showTestToast"
          class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
        >
          اختبار الإشعارات
        </button>

        <button
          @click="testCrypto"
          class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded"
        >
          اختبار التشفير
        </button>

        <button
          @click="goToLogin"
          class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded"
        >
          الذهاب إلى صفحة تسجيل الدخول
        </button>
      </div>

      <div v-if="hashResult" class="mt-4 p-4 bg-gray-100 rounded">
        <p><strong>النص الأصلي:</strong> {{ originalText }}</p>
        <p><strong>النص المشفر:</strong> {{ hashResult }}</p>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { showToast } from 'src/utils/interactive';
import { simpleHash, secureHash } from 'src/utils/crypto';

export default defineComponent({
  name: 'TestPage',
  data() {
    return {
      originalText: '',
      hashResult: '',
    };
  },
  methods: {
    showTestToast() {
      showToast({
        message: 'تم اختبار الإشعارات بنجاح!',
        type: 'success',
      });
    },

    async testCrypto() {
      try {
        this.originalText = 'اختبار التشفير ' + new Date().toISOString();

        // اختبار التشفير البسيط
        const simpleHashResult = simpleHash(this.originalText);
        console.log('Simple hash result:', simpleHashResult);

        // اختبار التشفير الآمن
        const secureHashResult = await secureHash(this.originalText);
        console.log('Secure hash result:', secureHashResult);

        this.hashResult = simpleHashResult;

        showToast({
          message: 'تم اختبار التشفير بنجاح!',
          type: 'success',
        });
      } catch (error) {
        console.error('Error testing crypto:', error);
        showToast({
          message: 'حدث خطأ أثناء اختبار التشفير: ' + error.message,
          type: 'error',
        });
      }
    },

    goToLogin() {
      this.$router.push('/login');
    }
  }
});
</script>
