<template>
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 20">
    <g fill="none" fill-rule="evenodd">
      <path
        :fill="lightColor"
        fill-rule="nonzero"
        d="M13.4,12.6 C12.9889642,12.8814251 12.4976701,13.0217949 12,13 C11.5023299,13.0217949 11.0110358,12.8814251 10.6,12.6 L0,6.9 L0,17 C2.02906125e-16,18.6568542 1.34314575,20 3,20 L21,20 C22.6568542,20 24,18.6568542 24,17 L24,6.9 L13.4,12.6 Z"
      />
      <path
        :fill="darkColor"
        d="M21,-4.4408921e-16 L3,-4.4408921e-16 C1.34314575,-8.8817842e-16 4.60042228e-16,1.34314575 2.57135486e-16,3 L2.57135486e-16,4 C-0.00194326986,4.36673226 0.187596922,4.7079046 0.5,4.9 L11.5,10.9 C11.6535417,10.9808367 11.8271782,11.015564 12,11 C12.1728218,11.015564 12.3464583,10.9808367 12.5,10.9 L23.5,4.9 C23.8124031,4.7079046 24.0019433,4.36673226 24,4 L24,3 C24,1.34314575 22.6568542,2.22044605e-15 21,-4.4408921e-16 Z"
      />
    </g>
  </svg>
</template>
<script>
import Base from '../base.vue';
export default {
  name: 'IconMail',
  extends: Base,
};
</script>
