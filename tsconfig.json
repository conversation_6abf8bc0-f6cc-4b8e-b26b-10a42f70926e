{"compilerOptions": {"target": "es2020", "module": "esnext", "strict": true, "allowJs": true, "jsx": "preserve", "importHelpers": true, "moduleResolution": "node", "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "sourceMap": true, "types": ["node"], "baseUrl": ".", "paths": {"src/*": ["src/*"], "schemas/*": ["schemas/*"], "main/*": ["main/*"], "backend/*": ["backend/*"], "regional/*": ["regional/*"], "fixtures/*": ["fixtures/*"], "reports/*": ["reports/*"], "models/*": ["models/*"], "utils/*": ["utils/*"], "dummy/*": ["dummy/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "ts-node": {"files": true}, "include": ["src/**/*.ts", "src/**/*.vue", "schemas/**/*.ts", "backend/**/*.ts", "fyo/**/*.ts", "models/**/*.ts", "patches/**/*.ts", "patches/**/*.js", "reports/**/*.ts", "accounting/**/*.ts", "scripts/**/*.ts", "main/**/*.ts", "regional/**/*.ts", "reports/**/*.ts", "utils/**/*.ts", "tests/**/*.ts", "dummy/**/*.ts"], "exclude": ["node_modules"]}