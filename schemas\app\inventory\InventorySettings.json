{"name": "InventorySettings", "label": "Inventory Settings", "isSingle": true, "isChild": false, "fields": [{"fieldname": "defaultLocation", "label": "Default Location", "fieldtype": "Link", "target": "Location", "create": true, "section": "<PERSON><PERSON><PERSON>"}, {"fieldname": "stockInHand", "label": "Stock In Hand Acc.", "fieldtype": "Link", "target": "Account", "section": "Accounts"}, {"fieldname": "stockReceivedButNotBilled", "label": "<PERSON> Received But Not Billed Acc.", "fieldtype": "Link", "target": "Account", "section": "Accounts"}, {"fieldname": "costOfGoodsSold", "label": "Cost Of Goods Sold Acc.", "fieldtype": "Link", "target": "Account", "section": "Accounts"}, {"fieldname": "enableBarcodes", "label": "Enable Barcodes", "fieldtype": "Check", "section": "Features"}, {"fieldname": "enableBatches", "label": "Enable Batches", "fieldtype": "Check", "section": "Features"}, {"fieldname": "enableSerialNumber", "label": "Enable Serial Number", "fieldtype": "Check", "section": "Features"}, {"fieldname": "enableUomConversions", "label": "Enable UOM Conversion", "fieldtype": "Check", "section": "Features"}, {"fieldname": "enableStockReturns", "label": "Enable Stock Returns", "fieldtype": "Check", "default": false, "section": "Features"}, {"fieldname": "enablePointOfSale", "label": "Enable Point of Sale", "fieldtype": "Check", "default": false, "section": "Features"}]}