/* استيراد ثيم النظام المحاسبي العصري */
@import './theme.css';

@font-face {
  font-family: 'Inter';
  font-weight: 100 900;
  font-display: swap;
  font-style: oblique 0deg 10deg;
  src: url('../assets/fonts/Inter.var.woff2') format('woff2');
}

* {
  outline-color: theme('colors.pink.400');
  font-variation-settings: 'slnt' 0deg;
}
.italic {
  font-variation-settings: 'slnt' 10deg;
}

@tailwind base;

@tailwind components;

@tailwind utilities;

html {
  color: theme('colors.black');
}

html.dark {
  color: theme('colors.black');
  background-color: theme('colors.gray.900');
  color-scheme: dark;
}

html.dark input[type='date']::-webkit-calendar-picker-indicator {
  background-color: transparent;
  color-scheme: dark;
}

input[type='number']::-webkit-inner-spin-button {
  appearance: none;
}

.window-drag {
  -webkit-app-region: drag;
}

.window-no-drag {
  -webkit-app-region: no-drag;
}

.grid {
  display: grid;
}

.inline-grid {
  display: inline-grid;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.frappe-chart .chart-legend {
  display: none;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

:root {
  --w-form: 600px;
  --w-app: 1200px;
  --w-sidebar: 14rem;
  --w-desk: calc(100vw - var(--w-sidebar));
  --w-desk-fixed: calc(var(--w-app) - var(--w-sidebar));
  --w-quick-edit: 22rem;
  --w-scrollbar: 0.6rem;
  --w-trafficlights: 72px;

  /* Row Heights */
  --h-row-smallest: 2rem;
  --h-row-small: 2.5rem;
  --h-row-mid: 3rem;
  --h-row-large: 3.5rem;
  --h-row-largest: 4rem;
  --h-app: 800px;
}

.backdrop {
  position: fixed;
  top: 0px;
  left: 0px;
  width: 100vw;
  height: 100vh;
  background: hsla(210, 83%, 41%, 0.102);
  backdrop-filter: blur(2px);
}

.w-form {
  width: var(--w-form);
}

.w-dialog {
  width: 24rem;
}

.w-toast {
  width: 24rem;
}

.w-quick-edit {
  width: var(--w-quick-edit);
  flex-shrink: 0;
}

.h-form {
  height: 800px;
}

.h-row-smallest {
  height: var(--h-row-smallest);
}

.h-row-small {
  height: var(--h-row-small);
}

.h-row-mid {
  height: var(--h-row-mid);
}

.h-row-large {
  height: var(--h-row-large);
}

.h-row-largest {
  height: var(--h-row-largest);
}

.w-sidebar {
  width: var(--w-sidebar);
}

.w-desk {
  width: var(--w-desk);
}

.show-mandatory::after {
  content: '*';
  display: inline-block;
  width: 0px;
  height: 0px;
  margin-left: -0.875rem;
  vertical-align: -3px;
  @apply text-red-500;
}

.custom-scroll::-webkit-scrollbar {
  width: var(--w-scrollbar);
  height: var(--w-scrollbar);
}

.custom-scroll::-webkit-scrollbar-track:vertical {
  border-left: solid 1px theme('colors.gray.100');
}

.custom-scroll::-webkit-scrollbar-track:horizontal {
  border-top: solid 1px theme('colors.gray.100');
}

.custom-scroll::-webkit-scrollbar-thumb {
  background: theme('colors.gray.100');
}

.custom-scroll::-webkit-scrollbar-thumb:hover {
  background: theme('colors.gray.200');
}

.custom-scroll::-webkit-scrollbar-corner {
  background: transparent;
}

.dark.custom-scroll::-webkit-scrollbar-track:vertical,
.dark .custom-scroll::-webkit-scrollbar-track:vertical {
  border-left-color: theme('colors.gray.800');
}

.dark.custom-scroll::-webkit-scrollbar-track:horizontal,
.dark .custom-scroll::-webkit-scrollbar-track:horizontal {
  border-top-color: theme('colors.gray.800');
}

.dark.custom-scroll-thumb1::-webkit-scrollbar-thumb,
.dark .custom-scroll-thumb1::-webkit-scrollbar-thumb {
  background: theme('colors.gray.850');
}

.dark.custom-scroll-thumb1::-webkit-scrollbar-thumb:hover,
.dark .custom-scroll-thumb1::-webkit-scrollbar-thumb:hover {
  background: theme('colors.gray.800');
}

.dark.custom-scroll-thumb2::-webkit-scrollbar-thumb,
.dark .custom-scroll-thumb2::-webkit-scrollbar-thumb {
  background: theme('colors.gray.800');
}

.dark.custom-scroll-thumb2::-webkit-scrollbar-thumb:hover,
.dark .custom-scroll-thumb2::-webkit-scrollbar-thumb:hover {
  background: theme('colors.gray.875');
}

/*
  Transitions
 */

.quickedit-enter-from,
.quickedit-leave-to {
  transform: translateX(var(--w-quick-edit));
  width: 0px;
  opacity: 0;
}

.quickedit-enter-to,
.quickedit-leave-from {
  transform: translateX(0px);
  width: var(--w-quick-edit);
  opacity: 1;
}

.quickedit-enter-active,
.quickedit-leave-active {
  transition: all 150ms ease-out;
}

/*
  RTL
 */

[dir='rtl'] .rtl-rotate-180 {
  transform: rotate(180deg);
}

[dir='rtl'] .custom-scroll::-webkit-scrollbar-track:vertical {
  border-right: solid 1px theme('colors.gray.200');
}

[dir='rtl'].dark.custom-scroll::-webkit-scrollbar-track:vertical,
[dir='rtl'].dark .custom-scroll::-webkit-scrollbar-track:vertical {
  border-right: solid 1px theme('colors.gray.850');
}

.pill {
  @apply py-0.5 px-1.5 rounded-md  text-xs;
  width: fit-content;
  height: fit-content;
}
.bg-gray-25 {
  --tw-bg-opacity: 1;
  /* background-color: rgba(230, 250, 250, var(--tw-bg-opacity)); */
  background-color: rgba(224, 242, 254, var(--tw-bg-opacity)); /* #E0F2FE */
}
.hover\:bg-blue-100:hover {
  -tw-bg-opacity: 1;
  background-color: rgba(224, 242, 254, var(--tw-bg-opacity)); /* #E0F2FE */
  border: 1px solid #38BDF8; /* إطار سماوي */
  border-radius: 0.5rem;
}
.hover\:bg-gray-100:hover {
  /* --tw-bg-opacity: 1;
  background-color: rgba(96, 165, 250, var(--tw-bg-opacity)); */
  -tw-bg-opacity: 1;
  background-color: rgba(224, 242, 254, var(--tw-bg-opacity)); /* #E0F2FE */
  border: 1px solid #38BDF8; /* إطار سماوي */
  border-radius: 0.5rem;
}
/* .bg-black {
  --tw-bg-opacity: 1;
  background-color: rgba(96, 165, 250, var(--tw-bg-opacity));
} */
.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgba(96, 165, 250, var(--tw-bg-opacity)); /* سماوي فاتح */
}
.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgba(23, 23, 23, var(--tw-text-opacity));
}
.bg-gray-100 {
  -tw-bg-opacity: 1;
  background-color: rgba(224, 242, 254, var(--tw-bg-opacity)); /* #E0F2FE */
  border: 1px solid #38BDF8; /* إطار سماوي */
  border-radius: 0.5rem;
}
.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgba(248, 248, 248, var(--tw-bg-opacity));
}
.bg-sidebar-light {
  background-color: #E0F2FE;
  border: 1px solid #38BDF8; /* إطار سماوي */
  border-radius: 0.5rem;
}
.w-sidebar {
  width: var(--w-sidebar);
  border: 1px solid #38BDF8;
  border-radius: 0.5rem;
}
.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgba(23, 23, 23, var(--tw-text-opacity));
  background-color: #f8f8f8;
}
.bg-gray-875 {
  --tw-bg-opacity: 1;
  background-color: #f8f8f8;
}