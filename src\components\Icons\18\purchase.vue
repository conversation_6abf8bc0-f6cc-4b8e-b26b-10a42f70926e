<template>
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 18">
    <g fill="none" fill-rule="evenodd" transform="translate(0 .5)">
      <path
        :fill="darkColor"
        fill-rule="nonzero"
        d="M0,4.3972168 L16,4.3972168 L16,15 C16,16.1045695 15.1045695,17 14,17 L2,17 C0.8954305,17 1.3527075e-16,16.1045695 0,15 L0,4.3972168 Z M8,12.2222222 C10.4,12.2222222 12.3636364,10.2722222 12.3636364,7.88888889 C12.3636364,7.45555556 12.0727273,7.16666667 11.6363636,7.16666667 C11.2,7.16666667 10.9090909,7.45555556 10.9090909,7.88888889 C10.9090909,9.47777778 9.6,10.7777778 8,10.7777778 C6.4,10.7777778 5.09090909,9.47777778 5.09090909,7.88888889 C5.09090909,7.45555556 4.8,7.16666667 4.36363636,7.16666667 C3.92727273,7.16666667 3.63636364,7.45555556 3.63636364,7.88888889 C3.63636364,10.2722222 5.6,12.2222222 8,12.2222222 Z"
      />
      <path
        :fill="lightColor"
        d="M0,3 L1.49222874,1.12925513 C2.06146543,0.415626854 2.92465315,1.0558663e-15 3.83750348,8.8817842e-16 L12.1683182,-4.4408921e-16 C13.0831751,-6.12145698e-16 13.9480333,0.417447486 14.5171563,1.13373106 L16,3 L0,3 Z"
      />
    </g>
  </svg>
</template>
<script>
import Base from '../base.vue';
export default {
  name: 'IconPurchase',
  extends: Base,
};
</script>
