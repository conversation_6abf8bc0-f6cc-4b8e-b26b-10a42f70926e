<template>
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18">
    <path
      fill="#FED73A"
      fill-rule="evenodd"
      d="M13.8000002,0 L0.600000009,0 C0.268800004,0 0,0.268800004 0,0.600000009 L0,13.8000002 C0,14.1312002 0.268800004,14.4000002 0.600000009,14.4000002 L13.8000002,14.4000002 C14.1312002,14.4000002 14.4000002,14.1312002 14.4000002,13.8000002 L14.4000002,0.600000009 C14.4000002,0.268800004 14.1312002,0 13.8000002,0 Z M4.80000007,4.80000007 C4.80000007,3.47460005 5.87460009,2.40000004 7.20000011,2.40000004 C8.52540013,2.40000004 9.60000014,3.47460005 9.60000014,4.80000007 L9.60000014,5.40000008 C9.60000014,6.7254001 8.52540013,7.80000012 7.20000011,7.80000012 C5.87460009,7.80000012 4.80000007,6.7254001 4.80000007,5.40000008 L4.80000007,4.80000007 Z M10.8000002,12.6000002 L3.60000005,12.6000002 C3.26880005,12.6000002 3.00000004,12.3312002 3.00000004,12.0000002 C3.00000004,10.3434002 4.34340006,9.00000013 6.00000009,9.00000013 L8.40000013,9.00000013 C10.0566001,9.00000013 11.4000002,10.3434002 11.4000002,12.0000002 C11.4000002,12.3312002 11.1312002,12.6000002 10.8000002,12.6000002 Z"
      transform="translate(2 2)"
    />
  </svg>
</template>
<script>
import Base from '../base.vue';
export default {
  extends: Base,
};
</script>
