<template>
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 18">
    <g fill="none" fill-rule="evenodd" transform="translate(0 .5)">
      <path
        :fill="lightColor"
        d="M4,8 L12,8 C14.1818182,8 16,6.18181818 16,4 C16,1.81818182 14.1818182,0 12,0 L4,0 C1.81818182,0 0,1.81818182 0,4 C0,6.18181818 1.81818182,8 4,8 Z M4,1.45454545 C5.38181818,1.45454545 6.54545455,2.61818182 6.54545455,4 C6.54545455,5.38181818 5.38181818,6.54545455 4,6.54545455 C2.61818182,6.54545455 1.45454545,5.38181818 1.45454545,4 C1.45454545,2.61818182 2.61818182,1.45454545 4,1.45454545 Z"
      />
      <path
        :fill="darkColor"
        d="M12,9 L4,9 C1.81818182,9 0,10.8181818 0,13 C0,15.1818182 1.81818182,17 4,17 L12,17 C14.1818182,17 16,15.1818182 16,13 C16,10.8181818 14.1818182,9 12,9 Z M12,15.5454545 C10.6181818,15.5454545 9.45454545,14.3818182 9.45454545,13 C9.45454545,11.6181818 10.6181818,10.4545455 12,10.4545455 C13.3818182,10.4545455 14.5454545,11.6181818 14.5454545,13 C14.5454545,14.3818182 13.3818182,15.5454545 12,15.5454545 Z"
      />
    </g>
  </svg>
</template>
<script>
import Base from '../base.vue';
export default {
  name: 'IconSettings',
  extends: Base,
};
</script>
