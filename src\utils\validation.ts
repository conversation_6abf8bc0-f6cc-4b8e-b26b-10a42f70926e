/**
 * Utility functions for validation
 */

/**
 * Validates an email address
 * @param value The email address to validate
 * @returns True if the email is valid, false otherwise
 */
export function validateEmail(value: string): boolean {
  if (!value) return true;
  
  // Basic email validation regex
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(value);
}

/**
 * Validates a phone number
 * @param value The phone number to validate
 * @returns True if the phone number is valid, false otherwise
 */
export function validatePhoneNumber(value: string): boolean {
  if (!value) return true;
  
  // Allow digits, spaces, plus, dash, and parentheses
  const phoneRegex = /^[0-9\s\+\-\(\)]+$/;
  return phoneRegex.test(value);
}

/**
 * Validates a URL
 * @param value The URL to validate
 * @returns True if the URL is valid, false otherwise
 */
export function validateUrl(value: string): boolean {
  if (!value) return true;
  
  try {
    new URL(value);
    return true;
  } catch {
    return false;
  }
}

/**
 * Validates a number is within a range
 * @param value The number to validate
 * @param min The minimum allowed value
 * @param max The maximum allowed value
 * @returns True if the number is within range, false otherwise
 */
export function validateNumberRange(value: number, min?: number, max?: number): boolean {
  if (value === undefined || value === null) return true;
  
  if (min !== undefined && value < min) return false;
  if (max !== undefined && value > max) return false;
  
  return true;
}

/**
 * Validates a string length is within a range
 * @param value The string to validate
 * @param minLength The minimum allowed length
 * @param maxLength The maximum allowed length
 * @returns True if the string length is within range, false otherwise
 */
export function validateStringLength(value: string, minLength?: number, maxLength?: number): boolean {
  if (!value) return true;
  
  if (minLength !== undefined && value.length < minLength) return false;
  if (maxLength !== undefined && value.length > maxLength) return false;
  
  return true;
}
