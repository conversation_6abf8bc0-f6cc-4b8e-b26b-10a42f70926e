<template>
  <div class="flex flex-col h-full">
    <PageHeader :title="t`معلومات الترخيص`">
      <Button
        v-if="!licenseInfo.isValid"
        type="primary"
        @click="$router.push('/license-activation')"
      >
        {{ t`تفعيل الترخيص` }}
      </Button>
      <Button
        v-else
        type="danger"
        @click="showDeactivateModal = true"
      >
        {{ t`إلغاء التفعيل` }}
      </Button>
    </PageHeader>

    <div class="flex-1 p-4 overflow-auto">
      <div class="max-w-4xl mx-auto space-y-6">
        <!-- حالة الترخيص -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">
            {{ t`حالة الترخيص` }}
          </h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="flex items-center">
              <span class="text-gray-600 dark:text-gray-400 mr-2">{{ t`الحالة:` }}</span>
              <span
                :class="[
                  'px-2 py-1 rounded-full text-sm font-medium',
                  licenseInfo.isValid 
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                    : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                ]"
              >
                {{ licenseInfo.isValid ? t`نشط` : t`غير نشط` }}
              </span>
            </div>
            
            <div class="flex items-center">
              <span class="text-gray-600 dark:text-gray-400 mr-2">{{ t`الأيام المتبقية:` }}</span>
              <span
                :class="[
                  'px-2 py-1 rounded-full text-sm font-medium',
                  licenseInfo.daysRemaining > 30 
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                    : licenseInfo.daysRemaining > 7
                    ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                    : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                ]"
              >
                {{ licenseInfo.daysRemaining }} {{ t`يوم` }}
              </span>
            </div>
          </div>
        </div>

        <!-- معلومات الترخيص -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">
            {{ t`تفاصيل الترخيص` }}
          </h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">
                {{ t`اسم الشركة` }}
              </label>
              <p class="text-gray-800 dark:text-white">
                {{ licenseInfo.companyName || t`غير محدد` }}
              </p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">
                {{ t`نوع الترخيص` }}
              </label>
              <p class="text-gray-800 dark:text-white">
                {{ getLicenseTypeLabel(licenseInfo.licenseType) }}
              </p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">
                {{ t`الحد الأقصى للمستخدمين` }}
              </label>
              <p class="text-gray-800 dark:text-white">
                {{ currentUserCount }} / {{ licenseInfo.maxUsers }}
              </p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">
                {{ t`تاريخ الانتهاء` }}
              </label>
              <p class="text-gray-800 dark:text-white">
                {{ formatDate(licenseInfo.expiryDate) }}
              </p>
            </div>
          </div>
        </div>

        <!-- الوحدات المسموحة -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">
            {{ t`الوحدات المسموحة` }}
          </h2>
          
          <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
            <div
              v-for="module in allModules"
              :key="module.value"
              :class="[
                'p-3 rounded-lg border text-center',
                isModuleAllowed(module.value)
                  ? 'bg-green-50 border-green-200 text-green-800 dark:bg-green-900 dark:border-green-700 dark:text-green-200'
                  : 'bg-gray-50 border-gray-200 text-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-400'
              ]"
            >
              <div class="text-sm font-medium">{{ module.label }}</div>
              <div class="text-xs mt-1">
                {{ isModuleAllowed(module.value) ? t`مسموح` : t`غير مسموح` }}
              </div>
            </div>
          </div>
        </div>

        <!-- إحصائيات الاستخدام -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">
            {{ t`إحصائيات الاستخدام` }}
          </h2>
          
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="text-center">
              <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {{ currentUserCount }}
              </div>
              <div class="text-sm text-gray-600 dark:text-gray-400">
                {{ t`المستخدمون النشطون` }}
              </div>
            </div>
            
            <div class="text-center">
              <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                {{ allowedModulesCount }}
              </div>
              <div class="text-sm text-gray-600 dark:text-gray-400">
                {{ t`الوحدات المتاحة` }}
              </div>
            </div>
            
            <div class="text-center">
              <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">
                {{ licenseInfo.daysRemaining }}
              </div>
              <div class="text-sm text-gray-600 dark:text-gray-400">
                {{ t`الأيام المتبقية` }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal لإلغاء التفعيل -->
    <Modal :open-modal="showDeactivateModal" @closemodal="showDeactivateModal = false">
      <div class="p-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
          {{ t`تأكيد إلغاء التفعيل` }}
        </h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          {{ t`هل أنت متأكد من رغبتك في إلغاء تفعيل الترخيص؟ سيؤدي هذا إلى إيقاف النظام.` }}
        </p>
        <div class="flex justify-end space-x-3 space-x-reverse">
          <Button
            type="secondary"
            @click="showDeactivateModal = false"
          >
            {{ t`إلغاء` }}
          </Button>
          <Button
            type="danger"
            :loading="isDeactivating"
            @click="deactivateLicense"
          >
            {{ t`إلغاء التفعيل` }}
          </Button>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { LicenseManager, LicenseInfo } from 'src/utils/licenseManager';
import { fyo } from 'src/initFyo';
import { showToast } from 'src/utils/interactive';
import PageHeader from 'src/components/PageHeader.vue';
import Button from 'src/components/Button.vue';
import Modal from 'src/components/Modal.vue';

export default defineComponent({
  name: 'LicenseInfo',
  components: {
    PageHeader,
    Button,
    Modal,
  },
  data() {
    return {
      licenseInfo: {
        isValid: false,
        isExpired: true,
        daysRemaining: 0,
        companyName: '',
        licenseType: '',
        maxUsers: 0,
        allowedModules: [],
        expiryDate: null,
      } as LicenseInfo,
      currentUserCount: 0,
      showDeactivateModal: false,
      isDeactivating: false,
      allModules: [
        { value: 'sales', label: this.t`المبيعات` },
        { value: 'purchases', label: this.t`المشتريات` },
        { value: 'inventory', label: this.t`المخزون` },
        { value: 'common', label: this.t`العام` },
        { value: 'reports', label: this.t`التقارير` },
        { value: 'pos', label: this.t`نقاط البيع` },
        { value: 'setup', label: this.t`الإعدادات` },
      ],
    };
  },
  computed: {
    allowedModulesCount() {
      if (this.licenseInfo.allowedModules.includes('all')) {
        return this.allModules.length;
      }
      return this.licenseInfo.allowedModules.length;
    },
  },
  async mounted() {
    await this.loadLicenseInfo();
    await this.loadUserCount();
  },
  methods: {
    async loadLicenseInfo() {
      try {
        const licenseManager = LicenseManager.getInstance();
        this.licenseInfo = await licenseManager.getLicenseInfo();
      } catch (error) {
        console.error('Error loading license info:', error);
        showToast({
          message: this.t`حدث خطأ أثناء تحميل معلومات الترخيص`,
          type: 'error',
        });
      }
    },
    
    async loadUserCount() {
      try {
        const users = await fyo.db.getAllRaw('User');
        this.currentUserCount = users.filter(user => user.isActive === 1).length;
      } catch (error) {
        console.error('Error loading user count:', error);
      }
    },
    
    getLicenseTypeLabel(type: string): string {
      const types = {
        trial: this.t`تجريبي`,
        standard: this.t`قياسي`,
        premium: this.t`مميز`,
        enterprise: this.t`مؤسسي`,
      };
      return types[type] || type;
    },
    
    formatDate(date: Date | null): string {
      if (!date) return this.t`غير محدد`;
      return new Date(date).toLocaleDateString('ar-SA');
    },
    
    isModuleAllowed(module: string): boolean {
      return this.licenseInfo.allowedModules.includes('all') || 
             this.licenseInfo.allowedModules.includes(module);
    },
    
    async deactivateLicense() {
      this.isDeactivating = true;
      try {
        const licenseManager = LicenseManager.getInstance();
        await licenseManager.deactivateLicense();
        
        showToast({
          message: this.t`تم إلغاء تفعيل الترخيص بنجاح`,
          type: 'success',
        });
        
        // إعادة توجيه إلى صفحة تفعيل الترخيص
        this.$router.push('/license-activation');
      } catch (error) {
        console.error('Error deactivating license:', error);
        showToast({
          message: this.t`حدث خطأ أثناء إلغاء تفعيل الترخيص`,
          type: 'error',
        });
      } finally {
        this.isDeactivating = false;
        this.showDeactivateModal = false;
      }
    },
  },
});
</script>
