{"name": "StockLedgerEntry", "label": "Stock Ledger Entry", "create": false, "isSingle": false, "isChild": false, "naming": "autoincrement", "fields": [{"label": "Entry No.", "fieldname": "name", "fieldtype": "Data", "required": true, "readOnly": true, "section": "<PERSON><PERSON><PERSON>"}, {"fieldname": "date", "label": "Date", "fieldtype": "Datetime", "readOnly": true, "section": "<PERSON><PERSON><PERSON>"}, {"fieldname": "location", "label": "Location", "fieldtype": "Link", "target": "Location", "readOnly": true, "section": "Details"}, {"fieldname": "batch", "label": "<PERSON><PERSON>", "fieldtype": "Link", "target": "<PERSON><PERSON>", "readOnly": true, "section": "Details"}, {"fieldname": "serialNumber", "label": "Serial Number", "fieldtype": "Link", "target": "SerialNumber", "readOnly": true, "section": "Details"}, {"fieldname": "item", "label": "<PERSON><PERSON>", "fieldtype": "Link", "target": "<PERSON><PERSON>", "readOnly": true, "section": "Details"}, {"fieldname": "rate", "label": "Rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "readOnly": true, "section": "Details"}, {"fieldname": "quantity", "label": "Quantity", "fieldtype": "Float", "readOnly": true, "section": "Details"}, {"fieldname": "referenceType", "label": "Ref. Type", "fieldtype": "Data", "readOnly": true, "section": "Reference"}, {"fieldname": "referenceName", "label": "Ref. Name", "fieldtype": "DynamicLink", "references": "referenceType", "readOnly": true, "section": "Reference"}]}