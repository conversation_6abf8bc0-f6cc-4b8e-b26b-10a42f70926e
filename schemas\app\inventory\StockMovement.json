{"name": "StockMovement", "label": "Stock Movement", "naming": "numberSeries", "isSingle": false, "isChild": false, "isSubmittable": true, "fields": [{"label": "Stock Movement No.", "fieldname": "name", "fieldtype": "Data", "required": true, "readOnly": true, "hidden": true}, {"fieldname": "numberSeries", "label": "Number Series", "fieldtype": "Link", "target": "NumberSeries", "create": true, "required": true, "default": "SMOV-", "section": "<PERSON><PERSON><PERSON>"}, {"fieldname": "movementType", "label": "Movement Type", "fieldtype": "Select", "options": [{"value": "MaterialIssue", "label": "Material Issue"}, {"value": "MaterialReceipt", "label": "Material Receipt"}, {"value": "MaterialTransfer", "label": "Material Transfer"}, {"value": "Manufacture", "label": "Manufacture"}], "required": true, "section": "<PERSON><PERSON><PERSON>"}, {"fieldname": "date", "label": "Date", "fieldtype": "Datetime", "required": true, "section": "<PERSON><PERSON><PERSON>"}, {"fieldname": "items", "label": "Items", "fieldtype": "Table", "target": "StockMovementItem", "required": true, "edit": true, "section": "Items"}, {"fieldname": "amount", "label": "Total Amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "readOnly": true, "section": "Items"}], "quickEditFields": ["numberSeries", "date", "movementType", "amount", "items"], "keywordFields": ["name", "movementType"]}