<template>
  <div class="sky-blue-demo">
    <PageHeader :title="t`عرض الثيم السماوي الهادئ`" />
    
    <div class="p-6 space-y-6">
      <!-- بطاقة الترحيب -->
      <div class="dashboard-card">
        <h2 class="text-2xl font-bold mb-4">🌤️ {{ t`مرحباً بك في الثيم السماوي الهادئ` }}</h2>
        <p class="theme-text-secondary text-lg mb-4">
          {{ t`ثيم مصمم خصيصاً ليكون مريحاً للعين مع ألوان سماوية هادئة وخطوط واضحة` }}
        </p>
        <div class="flex space-x-4 space-x-reverse">
          <button class="btn-primary">{{ t`زر أساسي` }}</button>
          <button class="btn-secondary">{{ t`زر ثانوي` }}</button>
        </div>
      </div>

      <!-- بطاقات الإحصائيات -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="stat-card">
          <div class="stat-number">1,234</div>
          <div class="stat-label">{{ t`إجمالي المبيعات` }}</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">567</div>
          <div class="stat-label">{{ t`العملاء النشطين` }}</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">89</div>
          <div class="stat-label">{{ t`المنتجات` }}</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">12</div>
          <div class="stat-label">{{ t`الفواتير اليوم` }}</div>
        </div>
      </div>

      <!-- نموذج إنشاء فاتورة -->
      <div class="invoice-header">
        <h2 class="text-xl font-bold">📄 {{ t`إنشاء فاتورة مبيعات جديدة` }}</h2>
      </div>
      <div class="invoice-body">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="form-group">
            <label>{{ t`اسم العميل` }}</label>
            <input type="text" class="form-input w-full" :placeholder="t`أدخل اسم العميل`" />
          </div>
          <div class="form-group">
            <label>{{ t`رقم الفاتورة` }}</label>
            <input type="text" class="form-input w-full" value="INV-2024-001" readonly />
          </div>
          <div class="form-group">
            <label>{{ t`تاريخ الفاتورة` }}</label>
            <input type="date" class="form-input w-full" />
          </div>
          <div class="form-group">
            <label>{{ t`طريقة الدفع` }}</label>
            <select class="form-input w-full">
              <option>{{ t`نقداً` }}</option>
              <option>{{ t`آجل` }}</option>
              <option>{{ t`بطاقة ائتمان` }}</option>
            </select>
          </div>
        </div>

        <!-- جدول الأصناف -->
        <div class="mt-6">
          <h3 class="theme-text-primary text-lg font-semibold mb-4">{{ t`أصناف الفاتورة` }}</h3>
          <div class="invoice-table">
            <table class="w-full">
              <thead class="table-header">
                <tr>
                  <th class="px-4 py-3 text-right">{{ t`الصنف` }}</th>
                  <th class="px-4 py-3 text-right">{{ t`الكمية` }}</th>
                  <th class="px-4 py-3 text-right">{{ t`السعر` }}</th>
                  <th class="px-4 py-3 text-right">{{ t`الإجمالي` }}</th>
                  <th class="px-4 py-3 text-right">{{ t`الإجراءات` }}</th>
                </tr>
              </thead>
              <tbody>
                <tr class="table-row">
                  <td class="px-4 py-3">{{ t`لابتوب Dell` }}</td>
                  <td class="px-4 py-3">2</td>
                  <td class="px-4 py-3">2,500 ريال</td>
                  <td class="px-4 py-3 theme-text-accent font-semibold">5,000 ريال</td>
                  <td class="px-4 py-3">
                    <button class="btn-sm btn-secondary">{{ t`تعديل` }}</button>
                  </td>
                </tr>
                <tr class="table-row">
                  <td class="px-4 py-3">{{ t`ماوس لاسلكي` }}</td>
                  <td class="px-4 py-3">5</td>
                  <td class="px-4 py-3">50 ريال</td>
                  <td class="px-4 py-3 theme-text-accent font-semibold">250 ريال</td>
                  <td class="px-4 py-3">
                    <button class="btn-sm btn-secondary">{{ t`تعديل` }}</button>
                  </td>
                </tr>
                <tr class="invoice-total-row">
                  <td colspan="3" class="px-4 py-3 text-right font-bold">{{ t`الإجمالي النهائي:` }}</td>
                  <td class="px-4 py-3 font-bold text-xl">5,250 ريال</td>
                  <td class="px-4 py-3"></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- أزرار الإجراءات -->
        <div class="flex justify-end space-x-4 space-x-reverse mt-6">
          <button class="btn-secondary">{{ t`حفظ كمسودة` }}</button>
          <button class="btn-primary btn-lg">{{ t`حفظ وطباعة` }}</button>
        </div>
      </div>

      <!-- قسم البحث والفلاتر -->
      <div class="dashboard-card">
        <h3 class="text-lg font-semibold mb-4">🔍 {{ t`البحث والفلاتر` }}</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label>{{ t`البحث` }}</label>
            <input type="text" class="search-input w-full" :placeholder="t`ابحث عن فاتورة أو عميل...`" />
          </div>
          <div>
            <label>{{ t`من تاريخ` }}</label>
            <input type="date" class="form-input w-full" />
          </div>
          <div>
            <label>{{ t`إلى تاريخ` }}</label>
            <input type="date" class="form-input w-full" />
          </div>
        </div>
      </div>

      <!-- التبويبات -->
      <div class="dashboard-card">
        <h3 class="text-lg font-semibold mb-4">📊 {{ t`التقارير` }}</h3>
        <div class="tab-container flex">
          <div class="tab-item active">{{ t`المبيعات` }}</div>
          <div class="tab-item">{{ t`المشتريات` }}</div>
          <div class="tab-item">{{ t`المخزون` }}</div>
          <div class="tab-item">{{ t`العملاء` }}</div>
        </div>
        <div class="mt-4">
          <div class="progress-bar">
            <div class="progress-fill" style="width: 75%"></div>
          </div>
          <p class="theme-text-secondary mt-2">{{ t`تقدم التحميل: 75%` }}</p>
        </div>
      </div>

      <!-- الإشعارات -->
      <div class="space-y-4">
        <h3 class="text-lg font-semibold">📢 {{ t`الإشعارات` }}</h3>
        <div class="notification success">
          <strong>{{ t`نجح!` }}</strong> {{ t`تم حفظ الفاتورة بنجاح` }}
        </div>
        <div class="notification warning">
          <strong>{{ t`تحذير!` }}</strong> {{ t`المخزون منخفض لبعض المنتجات` }}
        </div>
        <div class="notification info">
          <strong>{{ t`معلومة:` }}</strong> {{ t`يمكنك تخصيص الثيم من الإعدادات` }}
        </div>
      </div>

      <!-- معلومات الثيم -->
      <div class="dashboard-card">
        <h3 class="text-lg font-semibold mb-4">🎨 {{ t`معلومات الثيم السماوي` }}</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 class="theme-text-primary font-semibold mb-2">{{ t`المميزات:` }}</h4>
            <ul class="theme-text-secondary space-y-1">
              <li>• {{ t`ألوان سماوية هادئة ومريحة للعين` }}</li>
              <li>• {{ t`خطوط واضحة ومقروءة` }}</li>
              <li>• {{ t`تباين مثالي بين النصوص والخلفيات` }}</li>
              <li>• {{ t`تصميم عصري ومتجاوب` }}</li>
            </ul>
          </div>
          <div>
            <h4 class="theme-text-primary font-semibold mb-2">{{ t`الألوان المستخدمة:` }}</h4>
            <div class="grid grid-cols-4 gap-2">
              <div class="text-center">
                <div class="w-12 h-12 mx-auto mb-1 rounded-lg" style="background: #075985"></div>
                <p class="text-xs">{{ t`الشريط` }}</p>
              </div>
              <div class="text-center">
                <div class="w-12 h-12 mx-auto mb-1 rounded-lg" style="background: #F0F9FF"></div>
                <p class="text-xs">{{ t`المحتوى` }}</p>
              </div>
              <div class="text-center">
                <div class="w-12 h-12 mx-auto mb-1 rounded-lg" style="background: #0EA5E9"></div>
                <p class="text-xs">{{ t`المميز` }}</p>
              </div>
              <div class="text-center">
                <div class="w-12 h-12 mx-auto mb-1 rounded-lg" style="background: #0369A1"></div>
                <p class="text-xs">{{ t`الثانوي` }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import PageHeader from 'src/components/PageHeader.vue';

export default defineComponent({
  name: 'SkyBlueDemo',
  components: {
    PageHeader,
  },
});
</script>

<style scoped>
.sky-blue-demo {
  min-height: 100vh;
}
</style>
