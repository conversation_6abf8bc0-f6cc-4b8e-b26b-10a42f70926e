<template>
  <svg
    width="18"
    height="18"
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M6.81398 5.75342L5.67204 6.73249L3.9698 5.03025L3.00005 6L5.09165e-05 3L3.00005 0L6.00005 3L5.0303 3.96975L6.81398 5.75342ZM14.2051 10.455L17.2238 13.473C18.2596 14.5088 18.2596 16.1873 17.2238 17.223C16.1881 18.2588 14.5096 18.2588 13.4738 17.223L10.0853 13.8345L12.8161 10.4602C13.0441 10.4865 13.2713 10.5 13.5001 10.5C13.7386 10.5 13.9733 10.482 14.2051 10.455Z"
      :fill="lightColor"
    />
    <path
      d="M15.2033 5.07825L12.9218 2.79675L15.3278 0.39075C14.7691 0.14175 14.1518 0 13.5001 0C11.0146 0 9.00005 2.0145 9.00005 4.5C9.00005 4.9455 9.0668 5.3745 9.18755 5.781L1.0958 12.3285C0.427551 12.9187 0.0285509 13.7678 0.00155092 14.658C-0.0261991 15.549 0.319551 16.4212 0.949551 17.0505C1.56155 17.6632 2.3753 18 3.2408 18C4.17005 18 5.05655 17.601 5.67155 16.9042L12.2191 8.8125C12.6256 8.93325 13.0546 9 13.5001 9C15.9856 9 18.0001 6.9855 18.0001 4.5C18.0001 3.84825 17.8583 3.231 17.6093 2.6715L15.2033 5.07825Z"
      :fill="lightColor"
    />
  </svg>
</template>
<script>
import Base from '../base.vue';
export default {
  name: 'IconGeneral',
  extends: Base,
};
</script>
