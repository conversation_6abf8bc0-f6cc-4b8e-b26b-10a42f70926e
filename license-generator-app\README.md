# مولد التراخيص - أداة المطور

## نظرة عامة

تطبيق سطح مكتب لإنشاء مفاتيح الترخيص للعملاء. يعمل التطبيق بشكل مستقل ولا يتطلب اتصال بالإنترنت.

## المميزات

- 🔑 إنشاء مفاتيح ترخيص آمنة
- 📱 واجهة مستخدم سهلة وجميلة
- 📊 سجل كامل للتراخيص المُنشأة
- 📋 نسخ المفاتيح بنقرة واحدة
- 📤 تصدير السجل
- 🔒 أمان عالي مع التشفير
- ⚡ أداء سريع

## أنواع التراخيص

### 1. الترخيص التجريبي (Trial)
- مدة قابلة للتخصيص (افتراضي 7 أيام)
- 3 مستخدمين كحد أقصى
- وحدات محدودة (المبيعات، المخزون، العام)

### 2. الترخيص الدائم (Permanent)
- صالح لمدة 10 سنوات (عملياً دائم)
- 50 مستخدم كحد أقصى
- جميع الوحدات متاحة

## كيفية الاستخدام

### 1. تشغيل التطبيق
```bash
npm start
```

### 2. إنشاء ترخيص جديد
1. أدخل الرقم التسلسلي المُرسل من العميل
2. اختر نوع الترخيص (تجريبي أو دائم)
3. حدد المدة (للتراخيص التجريبية)
4. أدخل اسم العميل والملاحظات (اختياري)
5. انقر على "إنشاء مفتاح الترخيص"
6. انسخ المفتاح وأرسله للعميل

### 3. إدارة السجل
- عرض جميع التراخيص المُنشأة
- نسخ المفاتيح السابقة
- تصدير السجل كملف JSON

## التثبيت والبناء

### متطلبات النظام
- Node.js 16 أو أحدث
- npm أو yarn

### تثبيت التبعيات
```bash
npm install
```

### تشغيل التطبيق في وضع التطوير
```bash
npm run dev
```

### بناء التطبيق للإنتاج
```bash
# بناء لجميع المنصات
npm run build

# بناء لـ Windows فقط
npm run build-win

# إنشاء ملفات بدون تثبيت
npm run pack
```

## هيكل المشروع

```
license-generator-app/
├── src/
│   ├── main.js          # العملية الرئيسية لـ Electron
│   ├── index.html       # واجهة المستخدم
│   └── renderer.js      # منطق الواجهة
├── assets/
│   └── icon.png         # أيقونة التطبيق
├── package.json         # إعدادات المشروع
└── README.md           # هذا الملف
```

## الأمان

- يتم إنشاء المفاتيح باستخدام تشفير SHA-256
- كل مفتاح مرتبط بالرقم التسلسلي للجهاز
- لا يمكن تزوير أو تعديل المفاتيح
- يتم حفظ السجل محلياً فقط

## تنسيق المفاتيح

### مفتاح تجريبي
```
TRL-XXXX-XXXX-XXXX-XXXX
```

### مفتاح دائم
```
PRM-XXXX-XXXX-XXXX-XXXX
```

## ملفات البيانات

- **السجل**: يتم حفظه في `~/license-generator-history.json`
- **الحد الأقصى**: 100 ترخيص في السجل
- **النسخ الاحتياطي**: يُنصح بعمل نسخ احتياطية دورية

## استكشاف الأخطاء

### مشاكل شائعة

1. **"فشل في إنشاء مفتاح الترخيص"**
   - تأكد من صحة الرقم التسلسلي
   - أعد تشغيل التطبيق

2. **"حدث خطأ أثناء النسخ"**
   - تأكد من أن الحافظة متاحة
   - جرب النسخ يدوياً

3. **"فشل في تصدير السجل"**
   - تأكد من صلاحيات الكتابة
   - اختر مجلد آخر للحفظ

### سجلات الأخطاء

يمكن عرض سجلات الأخطاء من خلال:
- فتح أدوات المطور: `Ctrl+Shift+I`
- عرض وحدة التحكم (Console)

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966 XX XXX XXXX

## الترخيص

هذا التطبيق مخصص للاستخدام الداخلي للمطور فقط.
جميع الحقوق محفوظة © 2024

## إصدارات التطبيق

### الإصدار 1.0.0
- إنشاء مفاتيح الترخيص
- واجهة مستخدم أساسية
- سجل التراخيص
- تصدير البيانات

### التحديثات المستقبلية
- [ ] دعم قواعد بيانات خارجية
- [ ] تشفير إضافي للبيانات
- [ ] واجهة ويب اختيارية
- [ ] تقارير مفصلة
- [ ] نسخ احتياطي تلقائي
