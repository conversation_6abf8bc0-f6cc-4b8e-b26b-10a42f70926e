import { Doc } from 'fyo/model/doc';
import { ValidationMap } from 'fyo/model/types';
import { validateEmail } from 'src/utils/validation';
import { createHash } from 'crypto';

export class License extends Doc {
  licenseKey?: string;
  companyName?: string;
  contactEmail?: string;
  contactPhone?: string;
  issuedDate?: Date;
  expiryDate?: Date;
  maxUsers?: number;
  allowedModules?: string;
  isActive?: number;
  licenseType?: 'trial' | 'standard' | 'premium' | 'enterprise';
  machineId?: string;
  activationDate?: Date;
  lastValidation?: Date;
  validationCount?: number;

  validations: ValidationMap = {
    contactEmail: validateEmail,
  };

  // تحويل قيمة isActive إلى قيمة منطقية (boolean)
  get isActiveBoolean(): boolean {
    return this.isActive === 1;
  }

  // الحصول على قائمة الوحدات المسموحة
  get allowedModulesList(): string[] {
    if (!this.allowedModules) return [];
    try {
      return JSON.parse(this.allowedModules);
    } catch {
      return [];
    }
  }

  // تعيين قائمة الوحدات المسموحة
  setAllowedModules(modules: string[]): void {
    this.allowedModules = JSON.stringify(modules);
  }

  // التحقق من صحة الترخيص
  isValid(): boolean {
    if (!this.isActiveBoolean) return false;
    if (!this.expiryDate) return false;
    
    const now = new Date();
    const expiry = new Date(this.expiryDate);
    
    return now <= expiry;
  }

  // التحقق من انتهاء صلاحية الترخيص
  isExpired(): boolean {
    if (!this.expiryDate) return true;
    
    const now = new Date();
    const expiry = new Date(this.expiryDate);
    
    return now > expiry;
  }

  // الحصول على عدد الأيام المتبقية
  getDaysRemaining(): number {
    if (!this.expiryDate) return 0;
    
    const now = new Date();
    const expiry = new Date(this.expiryDate);
    const diffTime = expiry.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return Math.max(0, diffDays);
  }

  // التحقق من الوحدة المسموحة
  isModuleAllowed(module: string): boolean {
    const allowedModules = this.allowedModulesList;
    return allowedModules.includes(module) || allowedModules.includes('all');
  }

  // إنشاء معرف الجهاز - الكود الأصلي
  // static generateMachineId(): string {
  //   const os = require('os');
  //   const crypto = require('crypto');
  //
  //   const networkInterfaces = os.networkInterfaces();
  //   const cpus = os.cpus();
  //   const platform = os.platform();
  //   const arch = os.arch();
  //
  //   // جمع معلومات فريدة عن الجهاز
  //   const machineInfo = {
  //     platform,
  //     arch,
  //     cpuModel: cpus[0]?.model || '',
  //     totalMemory: os.totalmem(),
  //     hostname: os.hostname(),
  //   };
  //
  //   // إضافة عناوين MAC للشبكة
  //   const macAddresses: string[] = [];
  //   Object.values(networkInterfaces).forEach(interfaces => {
  //     interfaces?.forEach(iface => {
  //       if (iface.mac && iface.mac !== '00:00:00:00:00:00') {
  //         macAddresses.push(iface.mac);
  //       }
  //     });
  //   });
  //
  //   machineInfo['macAddresses'] = macAddresses.sort();
  //
  //   // إنشاء hash فريد
  //   const hash = crypto.createHash('sha256');
  //   hash.update(JSON.stringify(machineInfo));
  //
  //   return hash.digest('hex').substring(0, 32);
  // }

  // إنشاء معرف الجهاز - الكود الجديد مع الرقم التسلسلي
  static generateMachineId(): string {
    const os = require('os');
    const crypto = require('crypto');

    const networkInterfaces = os.networkInterfaces();
    const cpus = os.cpus();
    const platform = os.platform();
    const arch = os.arch();

    // جمع معلومات فريدة عن الجهاز
    const machineInfo = {
      platform,
      arch,
      cpuModel: cpus[0]?.model || '',
      totalMemory: os.totalmem(),
      hostname: os.hostname(),
    };

    // إضافة عناوين MAC للشبكة
    const macAddresses: string[] = [];
    Object.values(networkInterfaces).forEach(interfaces => {
      interfaces?.forEach(iface => {
        if (iface.mac && iface.mac !== '00:00:00:00:00:00') {
          macAddresses.push(iface.mac);
        }
      });
    });

    machineInfo['macAddresses'] = macAddresses.sort();

    // إنشاء hash فريد
    const hash = crypto.createHash('sha256');
    hash.update(JSON.stringify(machineInfo));

    return hash.digest('hex').substring(0, 32);
  }

  // إنشاء الرقم التسلسلي للعميل - دالة جديدة
  static generateSerialNumber(): string {
    const os = require('os');
    const crypto = require('crypto');

    const networkInterfaces = os.networkInterfaces();
    const cpus = os.cpus();
    const platform = os.platform();
    const arch = os.arch();

    // جمع معلومات الجهاز لإنشاء الرقم التسلسلي
    const machineInfo = {
      platform,
      arch,
      cpuModel: cpus[0]?.model || '',
      totalMemory: os.totalmem(),
      hostname: os.hostname(),
      userInfo: os.userInfo().username,
    };

    // جمع عناوين MAC
    const macAddresses: string[] = [];
    Object.values(networkInterfaces).forEach(interfaces => {
      interfaces?.forEach(iface => {
        if (iface.mac && iface.mac !== '00:00:00:00:00:00') {
          macAddresses.push(iface.mac);
        }
      });
    });

    // أخذ أول عنوان MAC كمعرف أساسي
    const primaryMac = macAddresses.sort()[0] || 'NO-MAC';
    machineInfo['primaryMac'] = primaryMac;

    // إنشاء hash للمعلومات
    const hash = crypto.createHash('sha256');
    hash.update(JSON.stringify(machineInfo));
    const fullHash = hash.digest('hex');

    // تنسيق الرقم التسلسلي: XXXX-XXXX-XXXX-XXXX
    const serialParts = [
      fullHash.substring(0, 4).toUpperCase(),
      fullHash.substring(4, 8).toUpperCase(),
      fullHash.substring(8, 12).toUpperCase(),
      fullHash.substring(12, 16).toUpperCase(),
    ];

    return serialParts.join('-');
  }

  // التحقق من الرقم التسلسلي مع معرف الجهاز - دالة جديدة
  static validateSerialNumber(serialNumber: string): boolean {
    const generatedSerial = License.generateSerialNumber();
    return generatedSerial === serialNumber;
  }

  // التحقق من معرف الجهاز
  validateMachineId(): boolean {
    if (!this.machineId) return true; // إذا لم يتم تعيين معرف الجهاز، فالترخيص صالح
    
    const currentMachineId = License.generateMachineId();
    return this.machineId === currentMachineId;
  }

  // تحديث آخر تحقق
  async updateLastValidation(): Promise<void> {
    this.lastValidation = new Date();
    this.validationCount = (this.validationCount || 0) + 1;
    await this.sync();
  }

  // تفعيل الترخيص
  async activate(): Promise<void> {
    this.activationDate = new Date();
    this.machineId = License.generateMachineId();
    this.isActive = 1;
    await this.sync();
  }

  // إلغاء تفعيل الترخيص
  async deactivate(): Promise<void> {
    this.isActive = 0;
    await this.sync();
  }

  // إنشاء مفتاح ترخيص بناءً على الرقم التسلسلي - دالة جديدة للمطور
  static generateLicenseKey(
    serialNumber: string,
    licenseType: 'trial' | 'permanent' = 'trial',
    durationDays: number = 7
  ): string {
    const crypto = require('crypto');

    // إزالة الشرطات من الرقم التسلسلي
    const cleanSerial = serialNumber.replace(/-/g, '');

    // إنشاء بيانات الترخيص
    const licenseData = {
      serial: cleanSerial,
      type: licenseType,
      duration: durationDays,
      timestamp: Date.now(),
    };

    // إنشاء hash للبيانات
    const hash = crypto.createHash('sha256');
    hash.update(JSON.stringify(licenseData));
    const licenseHash = hash.digest('hex');

    // تنسيق مفتاح الترخيص
    let prefix = '';
    switch (licenseType) {
      case 'trial':
        prefix = 'TRL';
        break;
      case 'permanent':
        prefix = 'PRM';
        break;
      default:
        prefix = 'GEN';
    }

    // تكوين المفتاح: PREFIX-XXXX-XXXX-XXXX-XXXX
    const keyParts = [
      prefix,
      licenseHash.substring(0, 4).toUpperCase(),
      licenseHash.substring(4, 8).toUpperCase(),
      licenseHash.substring(8, 12).toUpperCase(),
      licenseHash.substring(12, 16).toUpperCase(),
    ];

    return keyParts.join('-');
  }

  // التحقق من صحة مفتاح الترخيص مع الرقم التسلسلي - دالة جديدة
  static validateLicenseKeyWithSerial(licenseKey: string, serialNumber: string): {
    isValid: boolean;
    licenseType: string;
    error?: string;
  } {
    try {
      // التحقق من تنسيق المفتاح
      const keyParts = licenseKey.split('-');
      if (keyParts.length !== 5) {
        return { isValid: false, licenseType: '', error: 'تنسيق مفتاح الترخيص غير صحيح' };
      }

      const [prefix, ...hashParts] = keyParts;

      // تحديد نوع الترخيص
      let licenseType = '';
      switch (prefix) {
        case 'TRL':
          licenseType = 'trial';
          break;
        case 'PRM':
          licenseType = 'permanent';
          break;
        default:
          return { isValid: false, licenseType: '', error: 'نوع الترخيص غير معروف' };
      }

      // التحقق من الرقم التسلسلي
      if (!License.validateSerialNumber(serialNumber)) {
        return { isValid: false, licenseType: '', error: 'الرقم التسلسلي غير صحيح لهذا الجهاز' };
      }

      // إنشاء مفتاح متوقع للمقارنة
      const expectedKey = License.generateLicenseKey(serialNumber, licenseType as any, 7);
      const expectedHashParts = expectedKey.split('-').slice(1);

      // مقارنة أجزاء الـ hash
      const isHashValid = hashParts.every((part, index) => part === expectedHashParts[index]);

      if (!isHashValid) {
        return { isValid: false, licenseType: '', error: 'مفتاح الترخيص غير صحيح لهذا الجهاز' };
      }

      return { isValid: true, licenseType };

    } catch (error) {
      return { isValid: false, licenseType: '', error: 'خطأ في التحقق من مفتاح الترخيص' };
    }
  }
}
