import { Doc } from 'fyo/model/doc';
import { ValidationMap } from 'fyo/model/types';
import { validateEmail } from 'src/utils/validation';
import { createHash } from 'crypto';

export class License extends Doc {
  licenseKey?: string;
  companyName?: string;
  contactEmail?: string;
  contactPhone?: string;
  issuedDate?: Date;
  expiryDate?: Date;
  maxUsers?: number;
  allowedModules?: string;
  isActive?: number;
  licenseType?: 'trial' | 'standard' | 'premium' | 'enterprise';
  machineId?: string;
  activationDate?: Date;
  lastValidation?: Date;
  validationCount?: number;

  validations: ValidationMap = {
    contactEmail: validateEmail,
  };

  // تحويل قيمة isActive إلى قيمة منطقية (boolean)
  get isActiveBoolean(): boolean {
    return this.isActive === 1;
  }

  // الحصول على قائمة الوحدات المسموحة
  get allowedModulesList(): string[] {
    if (!this.allowedModules) return [];
    try {
      return JSON.parse(this.allowedModules);
    } catch {
      return [];
    }
  }

  // تعيين قائمة الوحدات المسموحة
  setAllowedModules(modules: string[]): void {
    this.allowedModules = JSON.stringify(modules);
  }

  // التحقق من صحة الترخيص
  isValid(): boolean {
    if (!this.isActiveBoolean) return false;
    if (!this.expiryDate) return false;
    
    const now = new Date();
    const expiry = new Date(this.expiryDate);
    
    return now <= expiry;
  }

  // التحقق من انتهاء صلاحية الترخيص
  isExpired(): boolean {
    if (!this.expiryDate) return true;
    
    const now = new Date();
    const expiry = new Date(this.expiryDate);
    
    return now > expiry;
  }

  // الحصول على عدد الأيام المتبقية
  getDaysRemaining(): number {
    if (!this.expiryDate) return 0;
    
    const now = new Date();
    const expiry = new Date(this.expiryDate);
    const diffTime = expiry.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return Math.max(0, diffDays);
  }

  // التحقق من الوحدة المسموحة
  isModuleAllowed(module: string): boolean {
    const allowedModules = this.allowedModulesList;
    return allowedModules.includes(module) || allowedModules.includes('all');
  }

  // إنشاء معرف الجهاز
  static generateMachineId(): string {
    const os = require('os');
    const crypto = require('crypto');
    
    const networkInterfaces = os.networkInterfaces();
    const cpus = os.cpus();
    const platform = os.platform();
    const arch = os.arch();
    
    // جمع معلومات فريدة عن الجهاز
    const machineInfo = {
      platform,
      arch,
      cpuModel: cpus[0]?.model || '',
      totalMemory: os.totalmem(),
      hostname: os.hostname(),
    };
    
    // إضافة عناوين MAC للشبكة
    const macAddresses: string[] = [];
    Object.values(networkInterfaces).forEach(interfaces => {
      interfaces?.forEach(iface => {
        if (iface.mac && iface.mac !== '00:00:00:00:00:00') {
          macAddresses.push(iface.mac);
        }
      });
    });
    
    machineInfo['macAddresses'] = macAddresses.sort();
    
    // إنشاء hash فريد
    const hash = crypto.createHash('sha256');
    hash.update(JSON.stringify(machineInfo));
    
    return hash.digest('hex').substring(0, 32);
  }

  // التحقق من معرف الجهاز
  validateMachineId(): boolean {
    if (!this.machineId) return true; // إذا لم يتم تعيين معرف الجهاز، فالترخيص صالح
    
    const currentMachineId = License.generateMachineId();
    return this.machineId === currentMachineId;
  }

  // تحديث آخر تحقق
  async updateLastValidation(): Promise<void> {
    this.lastValidation = new Date();
    this.validationCount = (this.validationCount || 0) + 1;
    await this.sync();
  }

  // تفعيل الترخيص
  async activate(): Promise<void> {
    this.activationDate = new Date();
    this.machineId = License.generateMachineId();
    this.isActive = 1;
    await this.sync();
  }

  // إلغاء تفعيل الترخيص
  async deactivate(): Promise<void> {
    this.isActive = 0;
    await this.sync();
  }
}
