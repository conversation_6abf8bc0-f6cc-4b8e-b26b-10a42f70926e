{"name": "license-generator", "version": "1.0.0", "description": "أداة إنشاء مفاتيح الترخيص", "main": "src/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "pack": "electron-builder --dir", "dist": "electron-builder"}, "build": {"appId": "com.company.license-generator", "productName": "م<PERSON><PERSON><PERSON> التراخيص", "directories": {"output": "dist"}, "files": ["src/**/*", "node_modules/**/*", "package.json"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}, "devDependencies": {"electron": "^22.0.0", "electron-builder": "^24.0.0"}, "dependencies": {"crypto": "^1.0.1"}, "author": "المطور", "license": "MIT"}