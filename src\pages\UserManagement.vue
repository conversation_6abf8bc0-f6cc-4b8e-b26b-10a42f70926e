<template>
  <div class="flex flex-col h-full">
    <PageHeader :title="t`إدارة المستخدمين`">
      <Button
        type="primary"
        @click="showNewUserModal = true"
      >
        {{ t`إضافة مستخدم جديد` }}
      </Button>
    </PageHeader>

    <div class="flex-1 p-4 overflow-auto">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th
                  scope="col"
                  class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  {{ t`اسم المستخدم` }}
                </th>
                <th
                  scope="col"
                  class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  {{ t`الاسم الكامل` }}
                </th>
                <th
                  scope="col"
                  class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  {{ t`البريد الإلكتروني` }}
                </th>
                <th
                  scope="col"
                  class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  {{ t`الدور` }}
                </th>
                <th
                  scope="col"
                  class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  {{ t`الحالة` }}
                </th>
                <th
                  scope="col"
                  class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  {{ t`الصلاحيات` }}
                </th>
                <th
                  scope="col"
                  class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  {{ t`الإجراءات` }}
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              <tr v-for="user in users" :key="user.name">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {{ user.name }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {{ user.fullName }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {{ user.email || '-' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  <span
                    :class="[
                      'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                      user.role === 'Admin' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                    ]"
                  >
                    {{ user.role === 'Admin' ? t`مدير` : t`مستخدم` }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  <span
                    :class="[
                      'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                      isUserActive(user) ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                    ]"
                  >
                    {{ isUserActive(user) ? t`نشط` : t`غير نشط` }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  <span
                    v-if="user.role === 'Admin'"
                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"
                  >
                    {{ t`جميع الصلاحيات` }}
                  </span>
                  <span
                    v-else-if="hasAllPermissions(user)"
                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                  >
                    {{ t`جميع الصلاحيات` }}
                  </span>
                  <span
                    v-else
                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
                  >
                    {{ t`صلاحيات محددة` }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button
                    @click="editUser(user)"
                    class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 ml-4"
                  >
                    {{ t`تعديل` }}
                  </button>
                  <button
                    @click="deleteUser(user)"
                    class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                  >
                    {{ t`حذف` }}
                  </button>
                </td>
              </tr>
              <tr v-if="users.length === 0">
                <td colspan="7" class="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                  {{ t`لا يوجد مستخدمين` }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Modal for adding/editing user -->
    <Modal :open-modal="showNewUserModal || !!editingUser" @closemodal="closeUserModal">
      <div class="p-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
          {{ editingUser ? t`تعديل المستخدم` : t`إضافة مستخدم جديد` }}
        </h3>

        <form @submit.prevent="saveUser">
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {{ t`اسم المستخدم` }}
            </label>
            <input
              v-model="userForm.name"
              type="text"
              :disabled="!!editingUser"
              class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              required
            />
          </div>

          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {{ t`الاسم الكامل` }}
            </label>
            <input
              v-model="userForm.fullName"
              type="text"
              class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              required
            />
          </div>

          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {{ t`البريد الإلكتروني` }}
            </label>
            <input
              v-model="userForm.email"
              type="email"
              class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
          </div>

          <div class="mb-4" v-if="!editingUser || showPasswordField">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {{ t`كلمة المرور` }}
            </label>
            <input
              v-model="userForm.password"
              type="password"
              class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              :required="!editingUser"
            />
          </div>

          <div v-if="editingUser && !showPasswordField" class="mb-4">
            <button
              type="button"
              @click="showPasswordField = true"
              class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
            >
              {{ t`تغيير كلمة المرور` }}
            </button>
          </div>

          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {{ t`الدور` }}
            </label>
            <select
              v-model="userForm.role"
              class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              required
            >
              <option value="Admin">{{ t`مدير` }}</option>
              <option value="User">{{ t`مستخدم` }}</option>
            </select>
          </div>

          <div class="mb-4">
            <label class="flex items-center">
              <input
                v-model="userForm.isActive"
                type="checkbox"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">{{ t`نشط` }}</span>
            </label>
          </div>

          <div class="mb-4">
            <label class="flex items-center">
              <input
                v-model="userForm.hasAllPermissions"
                type="checkbox"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">{{ t`لديه جميع الصلاحيات` }}</span>
            </label>
          </div>

          <div v-if="!userForm.hasAllPermissions && userForm.role !== 'Admin'" class="mb-4">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {{ t`الصلاحيات` }}
            </label>
            <div class="border rounded p-3 max-h-60 overflow-y-auto">
              <div v-for="module in availableModules" :key="module.value" class="mb-2">
                <label class="flex items-center">
                  <input
                    type="checkbox"
                    v-model="selectedModules"
                    :value="module.value"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">{{ module.label }}</span>
                </label>
              </div>
            </div>
          </div>

          <div class="flex justify-end space-x-3 space-x-reverse">
            <Button
              type="secondary"
              @click="closeUserModal"
            >
              {{ t`إلغاء` }}
            </Button>
            <Button
              type="primary"
              :loading="isSaving"
              submit
            >
              {{ t`حفظ` }}
            </Button>
          </div>
        </form>
      </div>
    </Modal>

    <!-- Confirmation Modal for Delete -->
    <Modal :open-modal="!!userToDelete" @closemodal="userToDelete = null">
      <div class="p-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
          {{ t`تأكيد الحذف` }}
        </h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          {{ t`هل أنت متأكد من رغبتك في حذف المستخدم` }} "{{ userToDelete?.fullName }}"؟
        </p>
        <div class="flex justify-end space-x-3 space-x-reverse">
          <Button
            type="secondary"
            @click="userToDelete = null"
          >
            {{ t`إلغاء` }}
          </Button>
          <Button
            type="danger"
            :loading="isDeleting"
            @click="confirmDeleteUser"
          >
            {{ t`حذف` }}
          </Button>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { fyo } from 'src/initFyo';
import { showToast } from 'src/utils/interactive';
import { simpleHash } from 'src/utils/crypto';
import { LicenseManager } from 'src/utils/licenseManager';
import PageHeader from 'src/components/PageHeader.vue';
import Button from 'src/components/Button.vue';
import Modal from 'src/components/Modal.vue';

export default defineComponent({
  name: 'UserManagement',
  components: {
    PageHeader,
    Button,
    Modal,
  },
  data() {
    return {
      users: [],
      showNewUserModal: false,
      editingUser: null,
      userToDelete: null,
      showPasswordField: false,
      isSaving: false,
      isDeleting: false,
      userForm: {
        name: '',
        fullName: '',
        email: '',
        password: '',
        role: 'User',
        isActive: true,
        hasAllPermissions: false,
      },
      selectedModules: [],
      availableModules: [
        { value: 'sales', label: this.t`المبيعات` },
        { value: 'purchases', label: this.t`المشتريات` },
        { value: 'inventory', label: this.t`المخزون` },
        { value: 'common', label: this.t`العام` },
        { value: 'reports', label: this.t`التقارير` },
        { value: 'pos', label: this.t`نقاط البيع` },
        { value: 'setup', label: this.t`الإعدادات` },
      ],
    };
  },
  async mounted() {
    await this.loadUsers();
  },
  methods: {
    async loadUsers() {
      try {
        const userList = await fyo.db.getAllRaw('User');
        console.log('Loaded users:', userList);

        // طباعة تفاصيل كل مستخدم للتشخيص
        if (userList && userList.length > 0) {
          userList.forEach((user, index) => {
            console.log(`User ${index + 1} details:`, {
              name: user.name,
              fullName: user.fullName,
              isActive: user.isActive,
              isActiveType: typeof user.isActive,
              role: user.role
            });
          });
        }

        // استخدام البيانات كما هي بدون تحويل
        this.users = userList;

        console.log('Processed users:', this.users);
      } catch (error) {
        console.error('Error loading users:', error);
        showToast({
          message: this.t`حدث خطأ أثناء تحميل المستخدمين`,
          type: 'error',
        });
      }
    },

    async editUser(user) {
      console.log('Editing user:', user);
      console.log('User full details:', user);

      // الحصول على وثيقة المستخدم الكاملة
      try {
        const fullUser = await fyo.doc.getDoc('User', user.name);
        console.log('Full user details:', fullUser);

        this.editingUser = fullUser;

        // تحويل حالة نشاط المستخدم إلى قيمة منطقية للاستخدام في مربع الاختيار
        const isActive = this.isUserActive(fullUser);
        console.log('Converted isActive for form:', isActive);

        // تحويل قيمة hasAllPermissions إلى قيمة منطقية
        const hasAllPermissions = fullUser.hasAllPermissions === 1 ||
                                 fullUser.hasAllPermissions === true ||
                                 fullUser.hasAllPermissions === '1' ||
                                 fullUser.hasAllPermissions === 'true';

        this.userForm = {
          name: fullUser.name,
          fullName: fullUser.fullName || '',
          email: fullUser.email || '',
          password: '',
          role: fullUser.role || 'User',
          isActive: isActive, // استخدام القيمة المحولة
          hasAllPermissions: hasAllPermissions,
        };

        console.log('User form after edit:', this.userForm);

        // تحميل صلاحيات المستخدم
        await this.loadUserPermissions(fullUser.name);

        this.showPasswordField = false;
      } catch (error) {
        console.error('Error loading full user details:', error);
        showToast({
          message: this.t`حدث خطأ أثناء تحميل بيانات المستخدم`,
          type: 'error',
        });
      }
    },

    async loadUserPermissions(userName) {
      try {
        // إعادة تعيين المصفوفة
        this.selectedModules = [];

        // تحميل الصلاحيات من localStorage فقط
        const storedPermissions = localStorage.getItem(`permissions_${userName}`);
        if (storedPermissions) {
          try {
            const parsedPermissions = JSON.parse(storedPermissions);
            console.log('Loaded permissions from localStorage:', parsedPermissions);

            if (parsedPermissions.modules && Array.isArray(parsedPermissions.modules)) {
              this.selectedModules = parsedPermissions.modules;
              console.log('Permissions loaded successfully:', this.selectedModules);
            }
          } catch (parseError) {
            console.error('Error parsing stored permissions:', parseError);
          }
        } else {
          console.log('No permissions found in localStorage for user:', userName);
        }

        console.log('Selected modules after loading:', this.selectedModules);
      } catch (error) {
        console.error('Error loading user permissions:', error);
        // لا نعرض رسالة خطأ للمستخدم لتجنب الإزعاج
      }
    },

    deleteUser(user) {
      this.userToDelete = user;
    },

    async confirmDeleteUser() {
      if (!this.userToDelete) return;

      this.isDeleting = true;
      try {
        await fyo.db.delete('User', this.userToDelete.name);

        showToast({
          message: this.t`تم حذف المستخدم بنجاح`,
          type: 'success',
        });

        await this.loadUsers();
        this.userToDelete = null;
      } catch (error) {
        console.error('Error deleting user:', error);
        showToast({
          message: this.t`حدث خطأ أثناء حذف المستخدم`,
          type: 'error',
        });
      } finally {
        this.isDeleting = false;
      }
    },

    async saveUser() {
      this.isSaving = true;
      try {
        console.log('Saving user form:', this.userForm);
        console.log('isActive type:', typeof this.userForm.isActive);
        console.log('isActive value:', this.userForm.isActive);

        // التحقق من حد المستخدمين عند إضافة مستخدم جديد
        if (!this.editingUser) {
          const licenseManager = LicenseManager.getInstance();
          const canAddUser = await licenseManager.checkUserLimit();

          if (!canAddUser) {
            showToast({
              message: this.t`تم الوصول إلى الحد الأقصى للمستخدمين المسموح به في الترخيص`,
              type: 'error',
            });
            this.isSaving = false;
            return;
          }
        }

        if (this.editingUser) {
          // Update existing user
          const user = await fyo.doc.getDoc('User', this.userForm.name);
          console.log('Existing user before update:', user);
          console.log('Existing user isActive:', user.isActive);
          console.log('Existing user isActive type:', typeof user.isActive);

          // Only update fields that have changed
          await user.set('fullName', this.userForm.fullName);
          await user.set('email', this.userForm.email);
          await user.set('role', this.userForm.role);

          // تحويل قيمة isActive إلى 1 أو 0
          const isActiveValue = this.userForm.isActive ? 1 : 0;
          console.log('Setting isActive to:', isActiveValue);
          console.log('Setting isActive type:', typeof isActiveValue);

          // استخدام دالة setActive في كائن المستخدم
          user.setActive(this.userForm.isActive);
          console.log('User isActive after setActive:', user.isActive);

          // تأكيد أن القيمة تم تعيينها بشكل صحيح
          await user.set('isActive', this.userForm.isActive ? 1 : 0);

          // تعيين قيمة hasAllPermissions
          await user.set('hasAllPermissions', this.userForm.hasAllPermissions ? 1 : 0);

          // تخزين قيمة hasAllPermissions في localStorage
          localStorage.setItem(`hasAllPermissions_${user.name}`, this.userForm.hasAllPermissions ? '1' : '0');

          // حفظ صلاحيات المستخدم
          await this.saveUserPermissions(user.name);

          // Only update password if it was changed
          if (this.showPasswordField && this.userForm.password) {
            // تشفير كلمة المرور
            const hashedPassword = simpleHash(this.userForm.password);
            console.log('Original password:', this.userForm.password);
            console.log('Hashed password:', hashedPassword);

            await user.set('password', hashedPassword);
          }

          await user.sync();

          showToast({
            message: this.t`تم تحديث المستخدم بنجاح`,
            type: 'success',
          });
        } else {
          // Create new user
          // تحويل قيمة isActive إلى 1 أو 0
          const isActiveValue = this.userForm.isActive ? 1 : 0;
          console.log('Creating new user with isActive:', isActiveValue);
          console.log('Creating new user isActive type:', typeof isActiveValue);

          // تشفير كلمة المرور
          const hashedPassword = simpleHash(this.userForm.password);
          console.log('Original password:', this.userForm.password);
          console.log('Hashed password:', hashedPassword);

          // إنشاء كائن بيانات المستخدم
          const userData = {
            name: this.userForm.name,
            fullName: this.userForm.fullName,
            email: this.userForm.email,
            password: hashedPassword, // استخدام كلمة المرور المشفرة
            role: this.userForm.role,
            isActive: isActiveValue,
          };

          console.log('User data for creation:', userData);
          const user = fyo.doc.getNewDoc('User', userData);

          // تأكيد أن حالة النشاط تم تعيينها بشكل صحيح
          user.setActive(this.userForm.isActive);
          console.log('User isActive after setActive:', user.isActive);

          // تعيين قيمة hasAllPermissions
          await user.set('hasAllPermissions', this.userForm.hasAllPermissions ? 1 : 0);

          // تخزين قيمة hasAllPermissions في localStorage
          localStorage.setItem(`hasAllPermissions_${user.name}`, this.userForm.hasAllPermissions ? '1' : '0');

          // حفظ صلاحيات المستخدم
          await this.saveUserPermissions(user.name);

          await user.sync();

          showToast({
            message: this.t`تم إنشاء المستخدم بنجاح`,
            type: 'success',
          });
        }

        this.closeUserModal();
        await this.loadUsers();
      } catch (error) {
        console.error('Error saving user:', error);
        showToast({
          message: this.t`حدث خطأ أثناء حفظ المستخدم`,
          type: 'error',
        });
      } finally {
        this.isSaving = false;
      }
    },

    async saveUserPermissions(userName) {
      try {
        console.log('Saving permissions for user:', userName);
        console.log('Selected modules:', this.selectedModules);

        // إذا كان المستخدم مديرًا أو لديه جميع الصلاحيات، فلا داعي لحفظ الصلاحيات الفردية
        if (this.userForm.role === 'Admin' || this.userForm.hasAllPermissions) {
          console.log('User is admin or has all permissions, skipping individual permissions');

          // حذف أي صلاحيات مخزنة سابقًا
          localStorage.removeItem(`permissions_${userName}`);
          return;
        }

        // حفظ الصلاحيات في localStorage فقط
        const userPermissions = {
          user: userName,
          modules: this.selectedModules,
          timestamp: new Date().toISOString() // إضافة طابع زمني للتتبع
        };

        localStorage.setItem(`permissions_${userName}`, JSON.stringify(userPermissions));
        console.log('User permissions saved to localStorage:', userPermissions);

        showToast({
          message: this.t`تم حفظ صلاحيات المستخدم بنجاح`,
          type: 'success',
        });
      } catch (error) {
        console.error('Error saving user permissions:', error);
        showToast({
          message: this.t`حدث خطأ أثناء حفظ صلاحيات المستخدم`,
          type: 'error',
        });
      }
    },

    closeUserModal() {
      this.showNewUserModal = false;
      this.editingUser = null;
      this.showPasswordField = false;
      this.selectedModules = [];
      this.userForm = {
        name: '',
        fullName: '',
        email: '',
        password: '',
        role: 'User',
        isActive: true,
        hasAllPermissions: false,
      };
      console.log('User modal closed, form reset');
    },

    // دالة مساعدة للتحقق من حالة نشاط المستخدم
    isUserActive(user) {
      if (!user) return false;

      // التحقق من حالة نشاط المستخدم بجميع الطرق الممكنة
      return user.isActive === 1 ||
             user.isActive === true ||
             user.isActive === '1' ||
             user.isActive === 'true';
    },

    // دالة مساعدة للتحقق من صلاحيات المستخدم
    hasAllPermissions(user) {
      if (!user) return false;

      // التحقق من قيمة hasAllPermissions في كائن المستخدم
      const hasAllInObject = user.hasAllPermissions === 1 ||
                            user.hasAllPermissions === true ||
                            user.hasAllPermissions === '1' ||
                            user.hasAllPermissions === 'true';

      // التحقق من قيمة hasAllPermissions في localStorage
      const storedValue = localStorage.getItem(`hasAllPermissions_${user.name}`);
      const hasAllInStorage = storedValue === '1' || storedValue === 'true';

      return hasAllInObject || hasAllInStorage;
    },
  },
});
</script>
