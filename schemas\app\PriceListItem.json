{"name": "PriceListItem", "label": "Price List Item", "isChild": true, "fields": [{"fieldname": "item", "label": "<PERSON><PERSON>", "fieldtype": "Link", "target": "<PERSON><PERSON>", "required": true, "create": true, "section": "<PERSON><PERSON>"}, {"fieldname": "unit", "label": "Unit Type", "fieldtype": "Link", "target": "UOM", "section": "<PERSON><PERSON>"}, {"fieldname": "rate", "label": "Rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "required": true, "section": "<PERSON><PERSON>"}], "tableFields": ["item", "unit", "rate"]}