{"name": "ClosingAmounts", "label": "Closing Amount", "isChild": true, "extends": "POSShiftAmounts", "fields": [{"fieldname": "openingAmount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Opening Amount", "placeholder": "Opening Amount", "readOnly": true}, {"fieldname": "closingAmount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Closing Amount", "placeholder": "Closing Amount"}, {"fieldname": "expectedAmount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Expected Amount", "placeholder": "Expected Amount", "readOnly": true}, {"fieldname": "differenceAmount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Difference Amount", "placeholder": "Difference Amount", "readOnly": true}], "tableFields": ["paymentMethod", "openingAmount", "closingAmount", "expectedAmount", "differenceAmount"]}