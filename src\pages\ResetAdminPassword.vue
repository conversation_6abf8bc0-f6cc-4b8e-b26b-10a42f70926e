<template>
  <div
    class="
      flex
      items-center
      justify-center
      h-full
      bg-gray-50
      dark:bg-gray-900
    "
  >
    <div
      class="
        w-96
        p-8
        bg-white
        dark:bg-gray-800
        rounded-lg
        shadow-lg
        border
        dark:border-gray-700
      "
    >
      <div class="text-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800 dark:text-white">
          {{ t`إعادة تعيين كلمة مرور المسؤول` }}
        </h1>
        <p class="text-gray-600 dark:text-gray-400 mt-2">
          {{ t`هذه الصفحة تقوم بإعادة تعيين كلمة مرور المستخدم المسؤول (admin) إلى القيمة الافتراضية` }}
        </p>
      </div>

      <div v-if="errorMessage" class="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
        {{ errorMessage }}
      </div>

      <div v-if="successMessage" class="mb-4 p-3 bg-green-100 text-green-700 rounded-md">
        {{ successMessage }}
      </div>

      <button
        @click="resetAdminPassword"
        class="
          w-full
          py-2
          px-4
          bg-blue-600
          hover:bg-blue-700
          text-white
          rounded-md
          focus:outline-none
          focus:ring-2
          focus:ring-blue-500
          focus:ring-offset-2
        "
        :disabled="isLoading"
      >
        <span v-if="isLoading">{{ t`جاري إعادة التعيين...` }}</span>
        <span v-else>{{ t`إعادة تعيين كلمة المرور` }}</span>
      </button>

      <div class="mt-4 text-center">
        <a
          href="/login"
          class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
        >
          {{ t`العودة إلى صفحة تسجيل الدخول` }}
        </a>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { fyo } from 'src/initFyo';
import { showToast } from 'src/utils/interactive';
import { simpleHash } from 'src/utils/crypto';

export default defineComponent({
  name: 'ResetAdminPassword',
  data() {
    return {
      errorMessage: '',
      successMessage: '',
      isLoading: false,
    };
  },
  methods: {
    async resetAdminPassword() {
      this.isLoading = true;
      this.errorMessage = '';
      this.successMessage = '';

      try {
        // التحقق من وجود المستخدم المسؤول
        const exists = await fyo.db.exists('User', 'admin');
        if (!exists) {
          // إنشاء مستخدم مسؤول جديد إذا لم يكن موجودًا
          console.log('Admin user does not exist, creating new admin user');
          
          const hashedPassword = simpleHash('admin');
          console.log('Hashed password for admin:', hashedPassword);
          
          const userData = {
            name: 'admin',
            fullName: 'المسؤول',
            password: hashedPassword,
            role: 'Admin',
            isActive: 1,
          };
          
          const user = fyo.doc.getNewDoc('User', userData);
          await user.sync();
          
          this.successMessage = this.t`تم إنشاء مستخدم مسؤول جديد بكلمة المرور الافتراضية: admin`;
        } else {
          // إعادة تعيين كلمة مرور المستخدم المسؤول الموجود
          console.log('Admin user exists, resetting password');
          
          const user = await fyo.doc.getDoc('User', 'admin');
          console.log('Admin user before update:', user);
          
          // تعيين كلمة المرور الجديدة
          const hashedPassword = simpleHash('admin');
          console.log('New hashed password for admin:', hashedPassword);
          
          await user.set('password', hashedPassword);
          await user.set('isActive', 1);
          await user.sync();
          
          this.successMessage = this.t`تم إعادة تعيين كلمة مرور المستخدم المسؤول إلى: admin`;
        }
        
        // إظهار رسالة نجاح
        showToast({
          message: this.t`تم إعادة تعيين كلمة المرور بنجاح`,
          type: 'success',
        });
      } catch (error) {
        console.error('Error resetting admin password:', error);
        this.errorMessage = this.t`حدث خطأ أثناء إعادة تعيين كلمة المرور: ${error.message || error}`;
      } finally {
        this.isLoading = false;
      }
    },
  },
});
</script>
