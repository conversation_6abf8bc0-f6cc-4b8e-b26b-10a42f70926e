# ملخص نظام الترخيص

## نظرة عامة

تم إنشاء نظام ترخيص شامل للمشروع يضمن الاستخدام المرخص فقط ويوفر تحكماً كاملاً في الوصول إلى الوحدات وعدد المستخدمين.

## المكونات الرئيسية

### 1. مخطط الترخيص (schemas/License.json)
- يحتوي على جميع الحقول اللازمة للترخيص
- يدعم أنواع مختلفة من التراخيص:
  * تجريبي (Trial)
  * قياسي (Standard) 
  * مميز (Premium)
  * مؤسسي (Enterprise)

الحقول الرئيسية:
- مفتاح الترخيص (licenseKey)
- اسم الشركة (companyName)
- البريد الإلكتروني للتواصل (contactEmail)
- تاريخ الإصدار (issuedDate)
- تاريخ الانتهاء (expiryDate)
- الحد الأقصى للمستخدمين (maxUsers)
- الوحدات المسموحة (allowedModules)
- نوع الترخيص (licenseType)
- معرف الجهاز (machineId)
- تاريخ التفعيل (activationDate)

### 2. نموذج الترخيص (models/baseModels/License/License.ts)
يحتوي على منطق التحقق من صحة الترخيص والدوال التالية:

**دوال التحقق:**
- `isValid()` - التحقق من صحة الترخيص
- `isExpired()` - التحقق من انتهاء صلاحية الترخيص
- `getDaysRemaining()` - الحصول على عدد الأيام المتبقية
- `isModuleAllowed(module)` - التحقق من الوحدة المسموحة
- `validateMachineId()` - التحقق من معرف الجهاز

**دوال الإدارة:**
- `generateMachineId()` - إنشاء معرف الجهاز (static)
- `activate()` - تفعيل الترخيص
- `deactivate()` - إلغاء تفعيل الترخيص
- `updateLastValidation()` - تحديث آخر تحقق

### 3. خدمة إدارة الترخيص (src/utils/licenseManager.ts)
تدير جميع عمليات الترخيص وتحتوي على:

**دوال التحميل والتحقق:**
- `loadLicense()` - تحميل الترخيص من قاعدة البيانات
- `validateLicense()` - التحقق الشامل من الترخيص
- `getLicenseInfo()` - الحصول على معلومات الترخيص

**دوال التفعيل:**
- `activateLicense(licenseKey, companyName, contactEmail)` - تفعيل الترخيص
- `deactivateLicense()` - إلغاء تفعيل الترخيص

**دوال التحكم في الوصول:**
- `isModuleAllowed(module)` - التحقق من صلاحية الوحدة
- `checkUserLimit()` - التحقق من عدد المستخدمين
- `periodicCheck()` - التحقق الدوري من الترخيص

### 4. واجهات المستخدم

#### صفحة تفعيل الترخيص (src/pages/LicenseActivation.vue)
- نموذج لإدخال مفتاح الترخيص
- حقول اسم الشركة والبريد الإلكتروني
- معلومات التواصل للحصول على ترخيص
- عرض مفاتيح الترخيص التجريبية
- التحقق التلقائي من وجود ترخيص صالح

#### صفحة معلومات الترخيص (src/pages/LicenseInfo.vue)
- عرض حالة الترخيص (نشط/غير نشط)
- تفاصيل الترخيص (الشركة، النوع، تاريخ الانتهاء)
- عرض الوحدات المسموحة وغير المسموحة
- إحصائيات الاستخدام (المستخدمون النشطون، الوحدات المتاحة)
- خيار إلغاء تفعيل الترخيص

### 5. التكامل مع النظام

#### حارس المسار (src/router.ts)
- التحقق من الترخيص قبل كل تنقل
- إعادة توجيه إلى صفحة التفعيل إذا كان الترخيص غير صالح
- التحقق الدوري التلقائي من الترخيص

#### القائمة الجانبية (src/utils/sidebarConfig.ts)
- التحقق من صلاحيات الوحدات قبل عرضها
- إخفاء الوحدات غير المسموحة في الترخيص
- عرض رابط معلومات الترخيص للمديرين

#### إدارة المستخدمين (src/pages/UserManagement.vue)
- التحقق من عدد المستخدمين قبل إضافة مستخدم جديد
- منع إضافة مستخدمين جدد عند الوصول للحد الأقصى

## مفاتيح الترخيص التجريبية

يمكن استخدام المفاتيح التالية للاختبار:

### 1. TRIAL-2024-001 (ترخيص تجريبي)
- الحد الأقصى للمستخدمين: 5
- الوحدات المسموحة: المبيعات، المخزون، العام
- صالح حتى: 2024-12-31

### 2. STANDARD-2024-001 (ترخيص قياسي)
- الحد الأقصى للمستخدمين: 10
- الوحدات المسموحة: المبيعات، المشتريات، المخزون، العام، التقارير
- صالح حتى: 2025-12-31

### 3. PREMIUM-2024-001 (ترخيص مميز)
- الحد الأقصى للمستخدمين: 25
- الوحدات المسموحة: جميع الوحدات
- صالح حتى: 2025-12-31

### 4. ENTERPRISE-2024-001 (ترخيص مؤسسي)
- الحد الأقصى للمستخدمين: 100
- الوحدات المسموحة: جميع الوحدات
- صالح حتى: 2026-12-31

## المميزات الأمنية

### 1. ربط الترخيص بالجهاز
- إنشاء معرف فريد للجهاز باستخدام معلومات الأجهزة
- منع استخدام الترخيص على أجهزة متعددة
- التحقق من معرف الجهاز عند كل تحقق من الترخيص

### 2. التحقق الدوري
- التحقق التلقائي من الترخيص كل 24 ساعة
- تحديث عداد مرات التحقق
- تسجيل آخر وقت تحقق

### 3. التحكم في الوصول
- التحقق من صلاحيات الوحدات قبل عرضها
- التحقق من عدد المستخدمين قبل الإضافة
- منع الوصول للنظام بدون ترخيص صالح

## كيفية الاستخدام

### 1. التثبيت الأولي
1. عند تشغيل النظام لأول مرة، سيتم توجيه المستخدم إلى صفحة تفعيل الترخيص
2. يدخل المستخدم مفتاح الترخيص ومعلومات الشركة
3. يتم التحقق من صحة المفتاح وتفعيل الترخيص
4. يتم إعادة توجيه المستخدم إلى النظام

### 2. الاستخدام اليومي
- يتم التحقق من الترخيص تلقائياً عند كل تنقل
- تظهر تحذيرات قبل انتهاء الصلاحية بـ 7 أيام
- يتم التحقق الدوري كل 24 ساعة

### 3. إدارة الترخيص
- يمكن للمديرين عرض معلومات الترخيص من القائمة الجانبية
- يمكن إلغاء تفعيل الترخيص عند الحاجة
- يمكن مراقبة استخدام النظام والوحدات

## إضافة مفاتيح ترخيص جديدة

لإضافة مفاتيح ترخيص جديدة، قم بتعديل دالة `validateLicenseKey` في ملف `licenseManager.ts`:

```javascript
const validLicenses = {
  'NEW-LICENSE-KEY': {
    issuedDate: new Date('2024-01-01'),
    expiryDate: new Date('2025-12-31'),
    maxUsers: 20,
    licenseType: 'standard',
    allowedModules: ['sales', 'purchases', 'inventory'],
  },
  // المزيد من المفاتيح
};
```

## الملاحظات الهامة

### الأمان
1. يتم ربط الترخيص بمعرف الجهاز لمنع الاستخدام على أجهزة متعددة
2. يتم التحقق من الترخيص عند كل تنقل في التطبيق
3. يتم التحقق الدوري من الترخيص كل 24 ساعة

### الاستخدام
1. يتم التحقق من عدد المستخدمين عند إضافة مستخدم جديد
2. يتم التحقق من صلاحيات الوحدات عند عرض القائمة الجانبية
3. يظهر تحذير قبل انتهاء صلاحية الترخيص بـ 7 أيام

### التطوير
1. يمكن تخصيص أنواع التراخيص وصلاحياتها
2. يمكن إضافة مفاتيح ترخيص جديدة بسهولة
3. يمكن تطوير نظام تحقق خارجي عبر API

## الدعم والصيانة

### معلومات التواصل
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966 XX XXX XXXX
- الموقع الإلكتروني: www.company.com

### الصيانة
- يُنصح بالتحقق من صحة الترخيص دورياً
- يُنصح بعمل نسخ احتياطية من بيانات الترخيص
- يُنصح بمراقبة استخدام النظام والوحدات
