{"name": "Invoice", "label": "Invoice", "isAbstract": true, "isSingle": false, "isChild": false, "isSubmittable": true, "fields": [{"label": "Invoice No", "fieldname": "name", "fieldtype": "Data", "required": true, "readOnly": true, "hidden": true}, {"abstract": true, "fieldname": "numberSeries", "section": "<PERSON><PERSON><PERSON>"}, {"abstract": true, "fieldname": "party", "section": "<PERSON><PERSON><PERSON>"}, {"fieldname": "account", "label": "Account", "fieldtype": "Link", "target": "Account", "create": true, "required": true}, {"fieldname": "date", "label": "Date", "fieldtype": "Datetime", "required": true, "section": "<PERSON><PERSON><PERSON>"}, {"fieldname": "priceList", "label": "Price List", "fieldtype": "Link", "target": "PriceList", "section": "<PERSON><PERSON><PERSON>"}, {"abstract": true, "fieldname": "items", "section": "Items"}, {"fieldname": "netTotal", "label": "Net Total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "readOnly": true, "section": "Items"}, {"fieldname": "taxes", "label": "Taxes", "fieldtype": "Table", "target": "TaxSummary", "readOnly": true, "section": "Tax and Totals"}, {"fieldname": "baseGrandTotal", "label": "Base Grand Total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "readOnly": true, "section": "Tax and Totals"}, {"fieldname": "grandTotal", "label": "Grand Total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "readOnly": true, "section": "Tax and Totals"}, {"fieldname": "setDiscountAmount", "label": "Set Discount Amount", "fieldtype": "Check", "default": false, "hidden": true}, {"fieldname": "discountAmount", "label": "Discount Amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "readOnly": false, "hidden": true}, {"fieldname": "discountPercent", "label": "Discount Percent", "fieldtype": "Float", "readOnly": false, "hidden": true}, {"fieldname": "entryCurrency", "label": "Entry Currency", "fieldtype": "Select", "options": [{"value": "Party", "label": "Party"}, {"value": "Company", "label": "Company"}], "default": "Party", "hidden": true}, {"fieldname": "currency", "label": "Customer <PERSON><PERSON><PERSON><PERSON>", "fieldtype": "Link", "target": "<PERSON><PERSON><PERSON><PERSON>", "readOnly": true, "hidden": true}, {"fieldname": "exchangeRate", "label": "Exchange Rate", "fieldtype": "Float", "default": 1, "readOnly": true, "hidden": true}, {"fieldname": "discountAfterTax", "label": "Apply Discount After Tax", "fieldtype": "Check", "default": false, "readOnly": false, "tab": "Settings"}, {"fieldname": "makeAutoPayment", "label": "Make Payment On Submit", "fieldtype": "Check", "default": false, "readOnly": false, "tab": "Settings"}, {"abstract": true, "fieldname": "makeAutoStockTransfer", "tab": "Settings"}, {"fieldname": "outstandingAmount", "label": "Outstanding Amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "readOnly": true, "section": "Outstanding"}, {"abstract": true, "fieldname": "stockNotTransferred", "section": "Outstanding"}, {"fieldname": "terms", "label": "Notes", "placeholder": "Add invoice terms", "fieldtype": "Text", "section": "References"}, {"fieldname": "attachment", "placeholder": "Add attachment", "label": "Attachment", "fieldtype": "Attachment", "section": "References"}, {"fieldname": "isReturned", "fieldtype": "Check", "hidden": true, "default": false}, {"abstract": true, "fieldname": "backReference", "section": "References"}, {"abstract": true, "fieldname": "returnAgainst", "section": "References"}], "keywordFields": ["name", "party"]}