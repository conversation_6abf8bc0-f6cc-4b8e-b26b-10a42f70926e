// إعدادات الثيم الجديد - Modern Accounting Theme
export interface ThemeConfig {
  name: string;
  colors: {
    sidebar: string;
    content: string;
    primary: string;
    secondary: string;
    accent: string;
  };
  darkMode: boolean;
}

// الثيمات المتاحة
export const themes: Record<string, ThemeConfig> = {
  modern: {
    name: 'Modern Blue',
    colors: {
      sidebar: 'bg-sidebar-800',
      content: 'bg-content-50',
      primary: 'bg-primary-600',
      secondary: 'bg-secondary-500',
      accent: 'bg-accent-500'
    },
    darkMode: false
  },
  dark: {
    name: 'Dark Professional',
    colors: {
      sidebar: 'bg-gray-900',
      content: 'bg-gray-800',
      primary: 'bg-primary-500',
      secondary: 'bg-secondary-600',
      accent: 'bg-accent-400'
    },
    darkMode: true
  },
  ocean: {
    name: 'Ocean Breeze',
    colors: {
      sidebar: 'bg-blue-900',
      content: 'bg-blue-50',
      primary: 'bg-blue-600',
      secondary: 'bg-teal-500',
      accent: 'bg-cyan-500'
    },
    darkMode: false
  },
  forest: {
    name: '<PERSON> Green',
    colors: {
      sidebar: 'bg-green-800',
      content: 'bg-green-50',
      primary: 'bg-green-600',
      secondary: 'bg-teal-600',
      accent: 'bg-emerald-500'
    },
    darkMode: false
  },
  sunset: {
    name: 'Sunset Orange',
    colors: {
      sidebar: 'bg-orange-800',
      content: 'bg-orange-50',
      primary: 'bg-orange-600',
      secondary: 'bg-amber-500',
      accent: 'bg-red-500'
    },
    darkMode: false
  },
  skyBlue: {
    name: 'Sky Blue - سماوي هادئ',
    colors: {
      sidebar: 'bg-sky-700',
      content: 'bg-sky-25',
      primary: 'bg-sky-600',
      secondary: 'bg-sky-500',
      accent: 'bg-blue-500'
    },
    darkMode: false
  },
  lightBlue: {
    name: 'Light Blue - أزرق فاتح',
    colors: {
      sidebar: 'bg-blue-600',
      content: 'bg-blue-25',
      primary: 'bg-blue-500',
      secondary: 'bg-blue-400',
      accent: 'bg-indigo-500'
    },
    darkMode: false
  }
};

// الثيم الافتراضي - السماوي الهادئ
let currentTheme: string = 'skyBlue';

export function setDarkMode(darkMode: boolean): void {
  if (darkMode) {
    document.documentElement.classList.add(
      'dark',
      'custom-scroll',
      'custom-scroll-thumb1'
    );
    return;
  }
  document.documentElement.classList.remove('dark');
}

// تطبيق الثيم
export function applyTheme(themeName: string): void {
  const theme = themes[themeName];
  if (!theme) return;

  currentTheme = themeName;

  // حفظ الثيم في localStorage
  localStorage.setItem('selectedTheme', themeName);

  // تطبيق الوضع المظلم/الفاتح
  setDarkMode(theme.darkMode);

  // إضافة كلاسات الثيم
  document.documentElement.setAttribute('data-theme', themeName);

  // تطبيق متغيرات CSS
  const root = document.documentElement;
  root.style.setProperty('--theme-sidebar', theme.colors.sidebar);
  root.style.setProperty('--theme-content', theme.colors.content);
  root.style.setProperty('--theme-primary', theme.colors.primary);
  root.style.setProperty('--theme-secondary', theme.colors.secondary);
  root.style.setProperty('--theme-accent', theme.colors.accent);
}

// الحصول على الثيم الحالي
export function getCurrentTheme(): string {
  return currentTheme;
}

// تحميل الثيم المحفوظ
export function loadSavedTheme(): void {
  const savedTheme = localStorage.getItem('selectedTheme') || 'skyBlue';
  applyTheme(savedTheme);
}

// الحصول على قائمة الثيمات
export function getAvailableThemes(): ThemeConfig[] {
  return Object.entries(themes).map(([key, theme]) => ({
    ...theme,
    key
  }));
}
