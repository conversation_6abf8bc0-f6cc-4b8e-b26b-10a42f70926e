const { ipcRenderer } = require('electron');

// متغيرات عامة
let currentLicense = null;
let licenseHistory = [];

// تحميل البيانات عند بدء التطبيق
document.addEventListener('DOMContentLoaded', async () => {
    await refreshHistory();
    showAlert('مرحباً بك في مولد التراخيص! 🎉', 'success');
});

// دالة تبديل عرض مدة الترخيص
function toggleDuration() {
    const licenseType = document.getElementById('licenseType').value;
    const durationGroup = document.getElementById('durationGroup');
    
    if (licenseType === 'permanent') {
        durationGroup.style.display = 'none';
    } else {
        durationGroup.style.display = 'block';
    }
}

// دالة الحصول على الرقم التسلسلي للجهاز الحالي
async function getMySerial() {
    try {
        const serialNumber = await ipcRenderer.invoke('get-serial-number');
        document.getElementById('serialNumber').value = serialNumber;
        showAlert('تم الحصول على الرقم التسلسلي للجهاز الحالي', 'success');
    } catch (error) {
        console.error('Error getting serial number:', error);
        showAlert('حدث خطأ أثناء الحصول على الرقم التسلسلي', 'error');
    }
}

// دالة إنشاء مفتاح الترخيص
async function generateLicense() {
    const serialNumber = document.getElementById('serialNumber').value.trim();
    const licenseType = document.getElementById('licenseType').value;
    const duration = parseInt(document.getElementById('duration').value) || 7;
    const clientName = document.getElementById('clientName').value.trim();
    const notes = document.getElementById('notes').value.trim();

    // التحقق من صحة البيانات
    if (!serialNumber) {
        showAlert('يرجى إدخال الرقم التسلسلي', 'error');
        return;
    }

    if (serialNumber.length < 10) {
        showAlert('الرقم التسلسلي غير صحيح', 'error');
        return;
    }

    // إظهار شاشة التحميل
    showLoading(true);
    hideResult();

    try {
        const result = await ipcRenderer.invoke('generate-license', {
            serialNumber,
            licenseType,
            duration,
            clientName,
            notes
        });

        if (result.success) {
            currentLicense = result.license;
            showResult(result.license);
            await refreshHistory();
            showAlert('تم إنشاء مفتاح الترخيص بنجاح! 🎉', 'success');
            
            // مسح النموذج
            clearForm();
        } else {
            showAlert(`حدث خطأ: ${result.error}`, 'error');
        }
    } catch (error) {
        console.error('Error generating license:', error);
        showAlert('حدث خطأ أثناء إنشاء مفتاح الترخيص', 'error');
    } finally {
        showLoading(false);
    }
}

// دالة عرض النتيجة
function showResult(license) {
    const resultSection = document.getElementById('resultSection');
    const licenseKeyElement = document.getElementById('licenseKey');
    const licenseInfoElement = document.getElementById('licenseInfo');

    licenseKeyElement.textContent = license.key;

    // عرض معلومات الترخيص
    const licenseTypeText = license.type === 'trial' ? 'تجريبي' : 'دائم';
    const createdAt = new Date(license.createdAt).toLocaleString('ar-SA');

    licenseInfoElement.innerHTML = `
        <div class="info-item">
            <strong>النوع:</strong> ${licenseTypeText}
        </div>
        <div class="info-item">
            <strong>الرقم التسلسلي:</strong> ${license.serialNumber}
        </div>
        <div class="info-item">
            <strong>تاريخ الإنشاء:</strong> ${createdAt}
        </div>
        ${license.type === 'trial' ? `
        <div class="info-item">
            <strong>المدة:</strong> ${license.duration} يوم
        </div>
        ` : ''}
        ${license.clientName ? `
        <div class="info-item">
            <strong>العميل:</strong> ${license.clientName}
        </div>
        ` : ''}
        ${license.notes ? `
        <div class="info-item">
            <strong>الملاحظات:</strong> ${license.notes}
        </div>
        ` : ''}
    `;

    resultSection.style.display = 'block';
    resultSection.scrollIntoView({ behavior: 'smooth' });
}

// دالة إخفاء النتيجة
function hideResult() {
    document.getElementById('resultSection').style.display = 'none';
}

// دالة نسخ مفتاح الترخيص
async function copyLicenseKey() {
    if (!currentLicense) return;

    try {
        const result = await ipcRenderer.invoke('copy-to-clipboard', currentLicense.key);
        if (result.success) {
            showAlert('تم نسخ مفتاح الترخيص! 📋', 'success');
        } else {
            showAlert('فشل في نسخ مفتاح الترخيص', 'error');
        }
    } catch (error) {
        console.error('Error copying license key:', error);
        showAlert('حدث خطأ أثناء النسخ', 'error');
    }
}

// دالة نسخ مفتاح من السجل
async function copyHistoryKey(key) {
    try {
        const result = await ipcRenderer.invoke('copy-to-clipboard', key);
        if (result.success) {
            showAlert('تم نسخ مفتاح الترخيص! 📋', 'success');
        } else {
            showAlert('فشل في نسخ مفتاح الترخيص', 'error');
        }
    } catch (error) {
        console.error('Error copying license key:', error);
        showAlert('حدث خطأ أثناء النسخ', 'error');
    }
}

// دالة تحديث السجل
async function refreshHistory() {
    try {
        licenseHistory = await ipcRenderer.invoke('get-license-history');
        updateHistoryTable();
    } catch (error) {
        console.error('Error refreshing history:', error);
        showAlert('حدث خطأ أثناء تحديث السجل', 'error');
    }
}

// دالة تحديث جدول السجل
function updateHistoryTable() {
    const tbody = document.getElementById('historyTableBody');
    
    if (licenseHistory.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="4" style="text-align: center; color: #6c757d; padding: 30px;">
                    لا يوجد تراخيص مُنشأة بعد
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = licenseHistory.map((license, index) => {
        const licenseTypeText = license.type === 'trial' ? 'تجريبي' : 'دائم';
        const badgeClass = license.type === 'trial' ? 'badge-trial' : 'badge-permanent';
        const createdAt = new Date(license.createdAt).toLocaleString('ar-SA');
        const clientName = license.clientName || 'غير محدد';

        return `
            <tr>
                <td>
                    <span class="badge ${badgeClass}">${licenseTypeText}</span>
                </td>
                <td>${clientName}</td>
                <td>${createdAt}</td>
                <td>
                    <button type="button" class="btn btn-copy" onclick="copyHistoryKey('${license.key}')">
                        📋 نسخ المفتاح
                    </button>
                </td>
            </tr>
        `;
    }).join('');
}

// دالة تصدير السجل
async function exportHistory() {
    try {
        const result = await ipcRenderer.invoke('export-history');
        if (result.success) {
            showAlert(`تم تصدير السجل بنجاح إلى: ${result.path}`, 'success');
        } else {
            showAlert(`فشل في تصدير السجل: ${result.error}`, 'error');
        }
    } catch (error) {
        console.error('Error exporting history:', error);
        showAlert('حدث خطأ أثناء تصدير السجل', 'error');
    }
}

// دالة إظهار/إخفاء شاشة التحميل
function showLoading(show) {
    const loading = document.getElementById('loading');
    loading.style.display = show ? 'block' : 'none';
}

// دالة عرض التنبيهات
function showAlert(message, type) {
    // إزالة التنبيهات السابقة
    const existingAlerts = document.querySelectorAll('.alert');
    existingAlerts.forEach(alert => alert.remove());

    // إنشاء تنبيه جديد
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.textContent = message;

    // إضافة التنبيه في بداية المحتوى
    const content = document.querySelector('.content');
    content.insertBefore(alert, content.firstChild);

    // إزالة التنبيه بعد 5 ثوان
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 5000);
}

// دالة مسح النموذج
function clearForm() {
    document.getElementById('serialNumber').value = '';
    document.getElementById('licenseType').value = 'trial';
    document.getElementById('duration').value = '7';
    document.getElementById('clientName').value = '';
    document.getElementById('notes').value = '';
    toggleDuration();
}

// إضافة مستمعي الأحداث
document.getElementById('licenseType').addEventListener('change', toggleDuration);

// تهيئة الواجهة
toggleDuration();
