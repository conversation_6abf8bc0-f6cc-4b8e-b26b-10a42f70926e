import { fyo } from 'src/initFyo';
import { License } from 'models/baseModels/License/License';
import { showToast } from 'src/utils/interactive';

export interface LicenseInfo {
  isValid: boolean;
  isExpired: boolean;
  daysRemaining: number;
  companyName: string;
  licenseType: string;
  maxUsers: number;
  allowedModules: string[];
  expiryDate: Date | null;
}

export class LicenseManager {
  private static instance: LicenseManager;
  private license: License | null = null;
  private lastCheck: Date | null = null;
  private checkInterval: number = 24 * 60 * 60 * 1000; // 24 ساعة

  private constructor() {}

  static getInstance(): LicenseManager {
    if (!LicenseManager.instance) {
      LicenseManager.instance = new LicenseManager();
    }
    return LicenseManager.instance;
  }

  // تحميل الترخيص
  async loadLicense(): Promise<License | null> {
    try {
      const exists = await fyo.db.exists('License', 'License');
      if (exists) {
        this.license = await fyo.doc.getDoc('License', 'License') as License;
        return this.license;
      }
      return null;
    } catch (error) {
      console.error('Error loading license:', error);
      return null;
    }
  }

  // التحقق من الترخيص
  async validateLicense(): Promise<LicenseInfo> {
    const license = await this.loadLicense();
    
    if (!license) {
      return {
        isValid: false,
        isExpired: true,
        daysRemaining: 0,
        companyName: '',
        licenseType: '',
        maxUsers: 0,
        allowedModules: [],
        expiryDate: null,
      };
    }

    const isValid = license.isValid() && license.validateMachineId();
    const isExpired = license.isExpired();
    const daysRemaining = license.getDaysRemaining();

    // تحديث آخر تحقق
    if (isValid) {
      await license.updateLastValidation();
    }

    return {
      isValid,
      isExpired,
      daysRemaining,
      companyName: license.companyName || '',
      licenseType: license.licenseType || '',
      maxUsers: license.maxUsers || 0,
      allowedModules: license.allowedModulesList,
      expiryDate: license.expiryDate || null,
    };
  }

  // تفعيل الترخيص
  async activateLicense(licenseKey: string, companyName: string, contactEmail: string): Promise<boolean> {
    try {
      // التحقق من صحة مفتاح الترخيص
      const licenseData = await this.validateLicenseKey(licenseKey);
      if (!licenseData) {
        showToast({
          message: 'مفتاح الترخيص غير صحيح',
          type: 'error',
        });
        return false;
      }

      // إنشاء أو تحديث الترخيص
      let license: License;
      const exists = await fyo.db.exists('License', 'License');
      
      if (exists) {
        license = await fyo.doc.getDoc('License', 'License') as License;
      } else {
        license = fyo.doc.getNewDoc('License', { name: 'License' }) as License;
      }

      // تعيين بيانات الترخيص
      await license.set('licenseKey', licenseKey);
      await license.set('companyName', companyName);
      await license.set('contactEmail', contactEmail);
      await license.set('issuedDate', licenseData.issuedDate);
      await license.set('expiryDate', licenseData.expiryDate);
      await license.set('maxUsers', licenseData.maxUsers);
      await license.set('licenseType', licenseData.licenseType);
      license.setAllowedModules(licenseData.allowedModules);

      // تفعيل الترخيص
      await license.activate();

      this.license = license;

      showToast({
        message: 'تم تفعيل الترخيص بنجاح',
        type: 'success',
      });

      return true;
    } catch (error) {
      console.error('Error activating license:', error);
      showToast({
        message: 'حدث خطأ أثناء تفعيل الترخيص',
        type: 'error',
      });
      return false;
    }
  }

  // التحقق من صحة مفتاح الترخيص
  private async validateLicenseKey(licenseKey: string): Promise<any> {
    // هنا يمكنك إضافة منطق التحقق من مفتاح الترخيص
    // يمكن أن يكون عبر API خارجي أو قاعدة بيانات محلية
    
    // مثال على التحقق المحلي (يجب استبداله بنظام حقيقي)
    const validLicenses = {
      'TRIAL-2024-001': {
        issuedDate: new Date('2024-01-01'),
        expiryDate: new Date('2024-12-31'),
        maxUsers: 5,
        licenseType: 'trial',
        allowedModules: ['sales', 'inventory', 'common'],
      },
      'STANDARD-2024-001': {
        issuedDate: new Date('2024-01-01'),
        expiryDate: new Date('2025-12-31'),
        maxUsers: 10,
        licenseType: 'standard',
        allowedModules: ['sales', 'purchases', 'inventory', 'common', 'reports'],
      },
      'PREMIUM-2024-001': {
        issuedDate: new Date('2024-01-01'),
        expiryDate: new Date('2025-12-31'),
        maxUsers: 25,
        licenseType: 'premium',
        allowedModules: ['all'],
      },
      'ENTERPRISE-2024-001': {
        issuedDate: new Date('2024-01-01'),
        expiryDate: new Date('2026-12-31'),
        maxUsers: 100,
        licenseType: 'enterprise',
        allowedModules: ['all'],
      },
    };

    return validLicenses[licenseKey] || null;
  }

  // التحقق من الوحدة المسموحة
  async isModuleAllowed(module: string): Promise<boolean> {
    const license = await this.loadLicense();
    if (!license) return false;
    
    if (!license.isValid() || !license.validateMachineId()) return false;
    
    return license.isModuleAllowed(module);
  }

  // التحقق من عدد المستخدمين
  async checkUserLimit(): Promise<boolean> {
    const license = await this.loadLicense();
    if (!license) return false;

    try {
      const users = await fyo.db.getAllRaw('User');
      const activeUsers = users.filter(user => user.isActive === 1);
      
      return activeUsers.length <= (license.maxUsers || 0);
    } catch (error) {
      console.error('Error checking user limit:', error);
      return false;
    }
  }

  // الحصول على معلومات الترخيص
  async getLicenseInfo(): Promise<LicenseInfo> {
    return await this.validateLicense();
  }

  // التحقق الدوري من الترخيص
  async periodicCheck(): Promise<void> {
    const now = new Date();
    
    if (this.lastCheck && (now.getTime() - this.lastCheck.getTime()) < this.checkInterval) {
      return; // لم يحن وقت التحقق بعد
    }

    const licenseInfo = await this.validateLicense();
    this.lastCheck = now;

    if (!licenseInfo.isValid) {
      showToast({
        message: 'الترخيص غير صالح أو منتهي الصلاحية',
        type: 'error',
      });
      return;
    }

    // تحذير قبل انتهاء الصلاحية
    if (licenseInfo.daysRemaining <= 7 && licenseInfo.daysRemaining > 0) {
      showToast({
        message: `تنتهي صلاحية الترخيص خلال ${licenseInfo.daysRemaining} أيام`,
        type: 'warning',
      });
    }
  }

  // إلغاء تفعيل الترخيص
  async deactivateLicense(): Promise<void> {
    const license = await this.loadLicense();
    if (license) {
      await license.deactivate();
      this.license = null;
    }
  }
}
