<template>
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18">
    <path
      fill="#8F6FD8"
      d="M5.40000008,7.20000011 C3.74580006,7.20000011 2.40000004,5.85420009 2.40000004,4.20000006 L2.40000004,3.00000004 C2.40000004,1.34580002 3.74580006,0 5.40000008,0 C7.05420011,0 8.40000013,1.34580002 8.40000013,3.00000004 L8.40000013,4.20000006 C8.40000013,5.85420009 7.05420011,7.20000011 5.40000008,7.20000011 Z M9.25380014,9.08700014 C10.1790002,9.43740014 10.8000002,10.3398002 10.8000002,11.3316002 L10.8000002,14.4000002 L0,14.4000002 L0,11.3316002 C0,10.3398002 0.621000009,9.43740014 1.54560002,9.08760014 C2.37480004,8.77320013 3.71640006,8.40000013 5.40000008,8.40000013 C7.08360011,8.40000013 8.42520013,8.77320013 9.25380014,9.08700014 Z M9.60000014,1.80000003 L14.4000002,1.80000003 L14.4000002,3.00000004 L9.60000014,3.00000004 L9.60000014,1.80000003 Z M9.60000014,4.80000007 L14.4000002,4.80000007 L14.4000002,6.00000009 L9.60000014,6.00000009 L9.60000014,4.80000007 Z M11.4000002,7.80000012 L14.4000002,7.80000012 L14.4000002,9.00000013 L11.4000002,9.00000013 L11.4000002,7.80000012 Z"
      transform="translate(2 2)"
    />
  </svg>
</template>
<script>
import Base from '../base.vue';
export default {
  extends: Base,
};
</script>
