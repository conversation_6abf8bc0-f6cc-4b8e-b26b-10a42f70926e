<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد التراخيص - أداة المطور</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 1.1rem;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 600;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
            width: 100%;
            margin-bottom: 15px;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn:active {
            transform: translateY(0);
        }

        .result {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            display: none;
        }

        .result.show {
            display: block;
        }

        .result h3 {
            color: #333;
            margin-bottom: 15px;
        }

        .license-key {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            word-break: break-all;
            margin-bottom: 15px;
        }

        .copy-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }

        .copy-btn:hover {
            background: #218838;
        }

        .info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }

        .info h4 {
            color: #1976d2;
            margin-bottom: 10px;
        }

        .info ul {
            color: #333;
            padding-right: 20px;
        }

        .info li {
            margin-bottom: 5px;
        }

        .machine-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .machine-info h4 {
            color: #856404;
            margin-bottom: 10px;
        }

        .machine-info p {
            color: #333;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔑 مولد التراخيص</h1>
            <p>أداة المطور لإنشاء تراخيص النظام المحاسبي</p>
        </div>

        <div class="machine-info">
            <h4>معلومات الجهاز الحالي:</h4>
            <p id="machineId">جاري تحميل معرف الجهاز...</p>
        </div>

        <form id="licenseForm">
            <div class="form-group">
                <label for="clientName">اسم العميل:</label>
                <input type="text" id="clientName" name="clientName" required placeholder="أدخل اسم العميل">
            </div>

            <div class="form-group">
                <label for="companyName">اسم الشركة:</label>
                <input type="text" id="companyName" name="companyName" required placeholder="أدخل اسم الشركة">
            </div>

            <div class="form-group">
                <label for="licenseType">نوع الترخيص:</label>
                <select id="licenseType" name="licenseType" required>
                    <option value="">اختر نوع الترخيص</option>
                    <option value="trial">تجريبي (30 يوم)</option>
                    <option value="permanent">دائم</option>
                    <option value="annual">سنوي</option>
                </select>
            </div>

            <div class="form-group" id="expiryGroup" style="display: none;">
                <label for="expiryDate">تاريخ الانتهاء:</label>
                <input type="date" id="expiryDate" name="expiryDate">
            </div>

            <div class="form-group">
                <label for="maxUsers">عدد المستخدمين المسموح:</label>
                <input type="number" id="maxUsers" name="maxUsers" value="5" min="1" max="100">
            </div>

            <div class="form-group">
                <label for="targetMachineId">معرف الجهاز المستهدف (اختياري):</label>
                <input type="text" id="targetMachineId" name="targetMachineId" placeholder="اتركه فارغاً لاستخدام الجهاز الحالي">
            </div>

            <button type="submit" class="btn">🔑 إنشاء الترخيص</button>
        </form>

        <div id="result" class="result">
            <h3>تم إنشاء الترخيص بنجاح! ✅</h3>
            <div class="license-key" id="licenseKey"></div>
            <button class="copy-btn" onclick="copyLicense()">📋 نسخ الترخيص</button>
        </div>

        <div class="info">
            <h4>📋 تعليمات الاستخدام:</h4>
            <ul>
                <li>أدخل بيانات العميل والشركة</li>
                <li>اختر نوع الترخيص المناسب</li>
                <li>حدد عدد المستخدمين المسموح</li>
                <li>انقر على "إنشاء الترخيص"</li>
                <li>انسخ الترخيص وأرسله للعميل</li>
            </ul>
        </div>
    </div>

    <script>
        // تحديد معرف الجهاز
        async function getMachineId() {
            try {
                // محاولة الحصول على معرف فريد للجهاز
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                ctx.textBaseline = 'top';
                ctx.font = '14px Arial';
                ctx.fillText('Machine ID Generator', 2, 2);
                
                const fingerprint = canvas.toDataURL();
                const machineId = btoa(fingerprint).substring(0, 16);
                
                document.getElementById('machineId').textContent = machineId;
                return machineId;
            } catch (error) {
                const fallbackId = 'DEV-' + Date.now().toString(36).toUpperCase();
                document.getElementById('machineId').textContent = fallbackId;
                return fallbackId;
            }
        }

        // تشفير البيانات
        function encryptData(data) {
            const key = 'FRAPPE_BOOKS_LICENSE_KEY_2024';
            let encrypted = '';
            for (let i = 0; i < data.length; i++) {
                encrypted += String.fromCharCode(
                    data.charCodeAt(i) ^ key.charCodeAt(i % key.length)
                );
            }
            return btoa(encrypted);
        }

        // إنشاء الترخيص
        function generateLicense(formData, machineId) {
            const licenseData = {
                clientName: formData.clientName,
                companyName: formData.companyName,
                licenseType: formData.licenseType,
                expiryDate: formData.expiryDate || null,
                maxUsers: parseInt(formData.maxUsers),
                machineId: formData.targetMachineId || machineId,
                issuedDate: new Date().toISOString(),
                version: '1.0'
            };

            const licenseString = JSON.stringify(licenseData);
            const encryptedLicense = encryptData(licenseString);
            
            return `FB-LICENSE-${encryptedLicense}`;
        }

        // نسخ الترخيص
        function copyLicense() {
            const licenseKey = document.getElementById('licenseKey').textContent;
            navigator.clipboard.writeText(licenseKey).then(() => {
                alert('تم نسخ الترخيص بنجاح! 📋');
            });
        }

        // معالج النموذج
        document.getElementById('licenseForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            const machineId = await getMachineId();
            const license = generateLicense(data, machineId);
            
            document.getElementById('licenseKey').textContent = license;
            document.getElementById('result').classList.add('show');
        });

        // معالج تغيير نوع الترخيص
        document.getElementById('licenseType').addEventListener('change', function() {
            const expiryGroup = document.getElementById('expiryGroup');
            const expiryInput = document.getElementById('expiryDate');
            
            if (this.value === 'trial') {
                // ترخيص تجريبي - 30 يوم من اليوم
                const trialDate = new Date();
                trialDate.setDate(trialDate.getDate() + 30);
                expiryInput.value = trialDate.toISOString().split('T')[0];
                expiryGroup.style.display = 'block';
            } else if (this.value === 'annual') {
                // ترخيص سنوي - سنة من اليوم
                const annualDate = new Date();
                annualDate.setFullYear(annualDate.getFullYear() + 1);
                expiryInput.value = annualDate.toISOString().split('T')[0];
                expiryGroup.style.display = 'block';
            } else if (this.value === 'permanent') {
                // ترخيص دائم - لا يحتاج تاريخ انتهاء
                expiryInput.value = '';
                expiryGroup.style.display = 'none';
            } else {
                expiryGroup.style.display = 'none';
            }
        });

        // تحميل معرف الجهاز عند تحميل الصفحة
        window.addEventListener('load', getMachineId);
    </script>
</body>
</html>
