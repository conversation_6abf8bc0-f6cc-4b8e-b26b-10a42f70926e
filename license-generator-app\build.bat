@echo off
echo ========================================
echo       مولد التراخيص - أداة البناء
echo ========================================
echo.

echo [1/4] تثبيت التبعيات...
call npm install
if %errorlevel% neq 0 (
    echo خطأ: فشل في تثبيت التبعيات
    pause
    exit /b 1
)

echo.
echo [2/4] تنظيف الملفات السابقة...
if exist dist rmdir /s /q dist

echo.
echo [3/4] بناء التطبيق...
call npm run build-win
if %errorlevel% neq 0 (
    echo خطأ: فشل في بناء التطبيق
    pause
    exit /b 1
)

echo.
echo [4/4] اكتمل البناء بنجاح!
echo.
echo ملفات التطبيق متوفرة في مجلد: dist
echo.
echo ========================================
echo           تم بنجاح! ✅
echo ========================================
pause
