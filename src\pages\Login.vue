<template>
  <div
    class="
      flex
      items-center
      justify-center
      h-full
      bg-gray-50
      dark:bg-gray-900
    "
  >
    <div
      class="
        w-96
        p-8
        bg-white
        dark:bg-gray-800
        rounded-lg
        shadow-lg
        border
        dark:border-gray-700
      "
    >
      <div class="text-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800 dark:text-white">
          {{ t`تسجيل الدخول` }}
        </h1>
        <p class="text-gray-600 dark:text-gray-400 mt-2">
          {{ t`الرجاء إدخال بيانات الدخول للوصول إلى النظام` }}
        </p>
      </div>

      <div v-if="errorMessage" class="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
        {{ errorMessage }}
      </div>

      <form @submit.prevent="login">
        <div class="mb-4">
          <label
            for="username"
            class="block text-gray-700 dark:text-gray-300 mb-2"
          >
            {{ t`اسم المستخدم` }}
          </label>
          <input
            id="username"
            v-model="username"
            type="text"
            class="
              w-full
              px-3
              py-2
              border
              rounded-md
              focus:outline-none
              focus:ring-2
              focus:ring-blue-500
              dark:bg-gray-700
              dark:border-gray-600
              dark:text-white
            "
            required
          />
        </div>

        <div class="mb-6">
          <label
            for="password"
            class="block text-gray-700 dark:text-gray-300 mb-2"
          >
            {{ t`كلمة المرور` }}
          </label>
          <input
            id="password"
            v-model="password"
            type="password"
            class="
              w-full
              px-3
              py-2
              border
              rounded-md
              focus:outline-none
              focus:ring-2
              focus:ring-blue-500
              dark:bg-gray-700
              dark:border-gray-600
              dark:text-white
            "
            required
          />
        </div>

        <button
          type="submit"
          class="
            w-full
            py-2
            px-4
            bg-blue-600
            hover:bg-blue-700
            text-white
            rounded-md
            focus:outline-none
            focus:ring-2
            focus:ring-blue-500
            focus:ring-offset-2
          "
          :disabled="isLoading"
        >
          <span v-if="isLoading">{{ t`جاري تسجيل الدخول...` }}</span>
          <span v-else>{{ t`تسجيل الدخول` }}</span>
        </button>
      </form>

      <div class="mt-4 text-center">
        <a
          href="/reset-password"
          class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mx-2"
        >
          {{ t`نسيت كلمة المرور؟` }}
        </a>
        <a
          href="/reset-admin"
          class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 mx-2"
        >
          {{ t`إعادة تعيين كلمة مرور المسؤول` }}
        </a>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { fyo } from 'src/initFyo';
import { showToast } from 'src/utils/interactive';

export default defineComponent({
  name: 'Login',
  data() {
    return {
      username: '',
      password: '',
      errorMessage: '',
      isLoading: false,
    };
  },
  methods: {
    async login() {
      this.isLoading = true;
      this.errorMessage = '';

      try {
        console.log('Attempting login with username:', this.username);

        // للتجربة: تسجيل الدخول مباشرة بدون التحقق من قاعدة البيانات
        if (this.username === 'admin' && this.password === 'admin') {
          console.log('Using hardcoded admin credentials');

          // Set current user and role in local storage
          localStorage.setItem('currentUser', this.username);
          localStorage.setItem('userRole', 'Admin');

          // Redirect to dashboard
          console.log('Redirecting to dashboard after login');
          // تغيير الشاشة النشطة إلى Desk
          window.location.href = '/';

          showToast({
            message: this.t`تم تسجيل الدخول بنجاح`,
            type: 'success',
          });

          return;
        }

        // التحقق من وجود مخطط المستخدم
        console.log('Checking if User schema exists...');
        const schemaExists = await fyo.db.exists('User');
        if (!schemaExists) {
          console.error('User schema does not exist!');
          this.errorMessage = this.t`مخطط المستخدم غير موجود. استخدم اسم المستخدم: admin وكلمة المرور: admin`;
          this.isLoading = false;
          return;
        }

        // Check if user exists
        console.log('Checking if user exists...');
        const exists = await fyo.db.exists('User', this.username);
        if (!exists) {
          console.log('User does not exist:', this.username);
          this.errorMessage = this.t`اسم المستخدم غير موجود`;
          this.isLoading = false;
          return;
        }

        // Get user document
        console.log('Getting user document...');
        const user = await fyo.doc.getDoc('User', this.username);

        // Verify password
        console.log('Verifying password...');
        console.log('Input password:', this.password);
        console.log('Stored hashed password:', user.password);

        // استخدام دالة التحقق من كلمة المرور في كائن المستخدم
        const isPasswordValid = user.verifyPassword(this.password);
        console.log('Password verification result:', isPasswordValid);

        if (!isPasswordValid) {
          console.log('Password verification failed');
          this.errorMessage = this.t`كلمة المرور غير صحيحة`;
          this.isLoading = false;
          return;
        }

        // Check if user is active
        console.log('Checking if user is active...');
        console.log('User isActive value:', user.isActive);
        console.log('User isActive type:', typeof user.isActive);

        // التحقق من أن المستخدم نشط بجميع الطرق الممكنة
        const isUserActive = user.isActive === 1 ||
                            user.isActive === true ||
                            user.isActive === '1' ||
                            user.isActive === 'true';

        console.log('Is user active (converted):', isUserActive);

        if (!isUserActive) {
          console.log('User account is not active');
          this.errorMessage = this.t`هذا الحساب غير نشط`;
          this.isLoading = false;
          return;
        }

        console.log('Login successful, setting user data in localStorage');
        // Set current user and role in local storage
        localStorage.setItem('currentUser', this.username);

        // طباعة دور المستخدم للتشخيص
        console.log('User role:', user.role);
        console.log('User role type:', typeof user.role);

        // تأكد من تعيين دور المستخدم بشكل صحيح
        if (user.role) {
          localStorage.setItem('userRole', user.role);
        } else {
          // إذا كان المستخدم هو admin، فاجعل دوره Admin
          if (this.username === 'admin') {
            localStorage.setItem('userRole', 'Admin');
          } else {
            localStorage.setItem('userRole', 'User');
          }
        }

        console.log('Stored user role in localStorage:', localStorage.getItem('userRole'));

        // Redirect to dashboard
        console.log('Redirecting to dashboard after login');
        // تغيير الشاشة النشطة إلى Desk
        window.location.href = '/';

        showToast({
          message: this.t`تم تسجيل الدخول بنجاح`,
          type: 'success',
        });
      } catch (error) {
        console.error('Login error:', error);
        this.errorMessage = this.t`حدث خطأ أثناء تسجيل الدخول: ${error.message || error}`;
      } finally {
        this.isLoading = false;
      }
    },
  },
});
</script>
